{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?a8f0", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?0b81", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?cb87", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?540b", "uni-app:///pages/pagesMy/sale_order.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?dbe2", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/sale_order.vue?b08e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "status", "list", "page", "limit", "loading", "onLoad", "onShareAppMessage", "console", "title", "imageUrl", "path", "methods", "getStatus", "getList", "type", "that", "res", "code", "msg", "time", "uni", "icon", "duration", "catch", "onReachBottom", "onPullDownRefresh", "getRefund", "content", "success", "id", "goDetail", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0D9uB;EACAC;IACA;MACAC;MAAA;;MAEAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MAAA;MACAC;IACA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAX;QACAC;QACAW;MACA;MACAC;QACA,gBAKAC;UAJAC;UACAnB;UACAoB;UACAC;QAEA;UACA;YACAJ;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAK;QACA;UACAA;YACAC;YACAb;YACAc;UACA;QACA;MACA,GACAC,sBAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAN;QACAZ;QACAmB;QACAC;UACA;YACA;cACAC;YACA;YACAd;cACA,iBAKAC;gBAJAC;gBACAnB;gBACAoB;gBACAC;cAEA;gBACAC;kBACAC;kBACAb;kBACAc;gBACA;gBACAP;gBACAA;gBACAA;gBACAA;cACA;gBACAK;kBACAC;kBACAb;kBACAc;gBACA;cACA;YACA,GACAC,sBAEA;UACA;YACAhB;UACA;QACA;MACA;IACA;IACA;IACAuB;MACAV;QACAW;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtMA;AAAA;AAAA;AAAA;AAA2hC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACA/iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/sale_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/sale_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./sale_order.vue?vue&type=template&id=4ee8bdee&\"\nvar renderjs\nimport script from \"./sale_order.vue?vue&type=script&lang=js&\"\nexport * from \"./sale_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sale_order.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/sale_order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sale_order.vue?vue&type=template&id=4ee8bdee&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sale_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sale_order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"order\">\r\n\t\t\t<scroll-view scroll-x=\"true\" class=\"nav\">\r\n\t\t\t\t<view :class=\"status == 0?'active':''\" @click=\"getStatus(0)\">全部</view>\r\n\t\t\t\t<view :class=\"status == 1?'active':''\" @click=\"getStatus(1)\">待核销</view>\r\n\t\t\t\t<view :class=\"status == 6?'active':''\" @click=\"getStatus(6)\">已领取</view>\r\n\t\t\t\t<view :class=\"status == 2?'active':''\" @click=\"getStatus(2)\">已完成</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class=\"list\" v-if=\"list.length > 0\">\r\n\t\t\t\t<view class=\"nr\" v-for=\"(items,index) in list\">\r\n\t\t\t\t\t<view class=\"numb\" @click=\"goDetail(items.id)\">\r\n\t\t\t\t\t\t<view>订单编号：{{items.order_sn}}</view>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '0'\">待付款</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 1\">已支付</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 2\">已核销</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 3\">已完成</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 4\">已退款</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 5\">待领取</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 6\">已领取</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '-1'\">已取消</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '-2'\">交易关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"goods\" @click=\"goDetail(items.id)\">\r\n\t\t\t\t\t\t<view class=\"product\" v-for=\"(item,index) in items.goods\">\r\n\t\t\t\t\t\t\t<image :src=\"api + item.goods_image\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.category_name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"num\">{{item.goods_name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\">￥{{item.goods_price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"money\" @click=\"goDetail(items.id)\">\r\n\t\t\t\t\t\t<view>优惠：￥{{items.coupon_amount || '0.00'}}，实付款：</view>\r\n\t\t\t\t\t\t<text>￥{{items.pay_fee || '0.00'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn\" v-if=\"items.status != '-1' && items.status != '-2' && items.status != 0 && items.status != 2 && items.status != 4\">\r\n\t\t\t\t\t\t<view v-if=\"items.status == 1 || items.status == 3 || items.status == 5\" @click=\"getRefund(items.id)\">申请退款</view>\r\n\t\t\t\t\t\t<button data-name=\"shareBtn\" open-type=\"share\" :data-orderid=\"items.id\" v-if=\"items.status == 1\">分享好友</button>\r\n\t\t\t\t\t\t<view class=\"active\" v-if=\"items.status == 1 || items.status == 5 || items.status == 6\" @click=\"goDetail(items.id)\">立即核销</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<uni-load-more :status=\"loading\"></uni-load-more>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"zw\" v-else>\r\n\t\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view>暂无订单</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\tstatus: 0,\r\n\r\n\t\t\t\tlist: [], // 订单列表\r\n\t\t\t\tpage: 1, // 分页\r\n\t\t\t\tlimit: 10, // 每页条数\r\n\t\t\t\tloading: \"noMore\", // noMore-无更多数据 loading-加载中 more-上拉加载更多\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tif (res.from === 'button') { // 来自页面内分享按钮\r\n\t\t\t\tconsole.log(res.target)\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '分享好友',\r\n\t\t\t\timageUrl: '/static/tc_bj.png',\r\n\t\t\t\tpath: '/pages/index/index?order_id=' + res.target.dataset.orderid,\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 状态切换\r\n\t\t\tgetStatus(e){\r\n\t\t\t\tthis.status = e\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 订单列表\r\n\t\t\tgetList() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\ttype: that.status,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.machineOrderList(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tif (that.page == 1) {\r\n\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (var i = 0; i < data.list.length; i++) {\r\n\t\t\t\t\t\t\tthat.list.push(data.list[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.list.length <= that.limit) {\r\n\t\t\t\t\t\t\tthat.loading = 'noMore';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.stopPullDownRefresh(); // 停止下拉刷新动画\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 上拉加载\r\n\t\t\tonReachBottom() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 下拉刷新\r\n\t\t\tonPullDownRefresh() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 退款\r\n\t\t\tgetRefund(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认退款？',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\tid: e,\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$api.machineOrderRefund(params).then(res => {\r\n\t\t\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\t\t\t\ttime\r\n\t\t\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: '退款成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthat.loading = 'loading'\r\n\t\t\t\t\t\t\t\t\tthat.list = []\r\n\t\t\t\t\t\t\t\t\tthat.page = 1\r\n\t\t\t\t\t\t\t\t\tthat.getList()\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 订单详情\r\n\t\t\tgoDetail(e) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: 'order_details?type=1' + '&id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.order {\r\n\t\tpadding: 110rpx 24rpx 20rpx;\r\n\t}\r\n\r\n\t.order .nav {\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twhite-space: nowrap;\r\n\t\t/*必须要有，规定段落中的文本不进行换行*/\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.order .nav view {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-block;\r\n\t\t/*必须要有*/\r\n\t\tmargin-right: 130rpx;\r\n\t}\r\n\t.order .nav view:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.order .nav view.active {\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.order .nav view.active::after {\r\n\t\tcontent: '';\r\n\t\twidth: 100%;\r\n\t\theight: 4rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #5A9AF1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.order .list .nr {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding-bottom: 20rpx;\r\n\t}\r\n\r\n\t.order .list .nr .numb {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t\tpadding: 0 22rpx;\r\n\t}\r\n\r\n\t.order .list .nr .numb view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.order .list .nr .numb text {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #5A9AF1;\r\n\t}\r\n\r\n\t.order .list .nr .goods {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product image {\r\n\t\twidth: 184rpx;\r\n\t\theight: auto;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product .xx {\r\n\t\twidth: calc(100% - 202rpx);\r\n\t}\r\n\r\n\t.order .list .nr .goods .product .xx .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product .xx .num {\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 48rpx;\r\n\t}\r\n\r\n\t.order .list .nr .goods .product .xx .price {\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.order .list .nr .money {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.order .list .nr .money view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.order .list .nr .money text {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.order .list .nr .btn{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t.order .list .nr .btn view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 62rpx;\r\n\t\tcolor: #161513;\r\n\t\tbackground: #F6F6F6;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-radius: 70rpx;\r\n\t\tmargin-right: 18rpx;\r\n\t}\r\n\t.order .list .nr .btn view.active{\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.order .list .nr .btn button{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 62rpx;\r\n\t\tcolor: #FC451E;\r\n\t\tbackground: #F6F6F6;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-radius: 70rpx;\r\n\t\tmargin: 0 18rpx 0 0;\r\n\t}\r\n\t.order .list .nr .btn button::after{\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.zw {\r\n\t\tmargin-top: 30%;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sale_order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sale_order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974046\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}