{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?481f", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?4ba1", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?473b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?6dad", "uni-app:///pages/pagesHome/goods_details.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?6c71", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/goods_details.vue?f322"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "navHeight", "statusBarHeight", "headerHeight", "backgroud", "show", "time", "type", "id", "infor", "banner", "number", "carNumb", "activities_tags", "content", "onLoad", "onShow", "methods", "getInfor", "that", "res", "code", "msg", "uni", "icon", "title", "duration", "catch", "getCarnum", "goHome", "url", "openSpecif", "changeNum", "getnumber", "blur", "getSubmit", "goods_list", "goCar", "closeSpecif", "back", "onPageScroll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyFjvB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACAV;QACAD;MACA;MACAY;QACA,gBAIAC;UAHAC;UACAtB;UACAuB;QAEA;UACAH;UACAA;UACA;YACA;cACA;YACA;UACA;UACA;YACA;cACA;YACA;UACA;QACA;UACAI;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;MACAT;QACA;UACAA;QACA;UACAI;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAE;MACAN;QACAO;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACArB;QACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;UACAY;YACAC;YACAC;YACAC;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IAGA;IACAO;MACA;MACA;MACA;QACA;MACA;QACA;UACAV;YACAC;YACAC;YACAC;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAZ;UACAC;UACAC;UACAC;QACA;MACA;QACA;UACA;UACA;YACAU,aACA;cACA;cACA;YACA;UAEA;UACAjB;YACA,iBAIAC;cAHAC;cACAtB;cACAuB;YAEA;cACAC;gBACAC;gBACAC;gBACAC;cACA;cACAP;cACAA;YACA;cACAI;gBACAC;gBACAC;gBACAC;cACA;YACA;UACA,GACAC,sBAEA;QACA;UACA;UACA,iBACA;YACA;YACA;YACA;UACA,EACA;UACAR;UACAA;UACAI;YACAO;UACA;QACA;MACA;IACA;IAEA;IACAO;MACA;QACA;MACA;QACAd;UACAO;QACA;MACA;IACA;IAGA;IACAQ;MACA;IACA;IAGAC;MACAhB;IACA;IAGAiB;MACA;QACA;MACA;QAAA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAA8hC,CAAgB,88BAAG,EAAC,C;;;;;;;;;;;ACAljC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/goods_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/goods_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goods_details.vue?vue&type=template&id=7e82a1b8&\"\nvar renderjs\nimport script from \"./goods_details.vue?vue&type=script&lang=js&\"\nexport * from \"./goods_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goods_details.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/goods_details.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods_details.vue?vue&type=template&id=7e82a1b8&\"", "var components\ntry {\n  components = {\n    uniNavTouming: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-touming/uni-nav-touming\" */ \"@/components/uni-nav-touming/uni-nav-touming.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods_details.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- #ifndef MP-ALIPAY -->\r\n\t\t<uni-nav-touming left-icon=\"back\" :title=\"title\" :color=\"'#000'\" :background-color=\"backgroud\" fixed=\"true\" @clickLeft=\"back\" status-bar=\"true\"></uni-nav-touming>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"banner_bg\" :style=\"{marginTop: '-' +  headerHeight + 'px'}\">\r\n\t\t\t<swiper autoplay='true' circular=\"true\" interval='5000' duration='500' class='banner'>\r\n\t\t\t\t<swiper-item class=\"item_image\" v-for=\"(item,index) in banner\">\r\n\t\t\t\t\t<image :src='api + item' class='swiper_image' mode=\"aspectFill\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<view class=\"goods\">\r\n\t\t\t<view class=\"money\">\r\n\t\t\t\t<view class=\"num\">￥{{infor.price || 0.00}}</view>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view class=\"price\">原价：￥{{infor.market_price || 0.00}}</view>\r\n\t\t\t\t\t<text>|</text>\r\n\t\t\t\t\t<view class=\"sold\">已售：{{infor.shop_sales || 0}}{{infor.unit}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gname\">{{infor.name}}</view>\r\n\t\t\t<view class=\"activity\" v-if=\"infor.activities_tags\">\r\n\t\t\t\t<view class=\"bt\">活动：</view>\r\n\t\t\t\t<rich-text :nodes=\"activities_tags\" class=\"xx\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"details\">\r\n\t\t\t\t<view class=\"title\">商品详情</view>\r\n\t\t\t\t<rich-text :nodes=\"content\" class=\"xx\"></rich-text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"foot\">\r\n\t\t\t<view class=\"nav\">\r\n\t\t\t\t<view class=\"xx\" @click=\"goHome\">\r\n\t\t\t\t\t<image src=\"/static/foot_syb.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<view class=\"name\">商城首页</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xx\" @click=\"goCar\">\r\n\t\t\t\t\t<view class=\"num\" v-if=\"carNumb > 0\">\r\n\t\t\t\t\t\t<text>{{carNumb}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/icon_carh.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<view class=\"car\">购物车</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn\">\r\n\t\t\t\t<view class=\"add\" @click=\"openSpecif(1)\">加入购物车</view>\r\n\t\t\t\t<view class=\"pay\" @click=\"openSpecif(2)\">立即购买</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<uni-popup ref=\"specif\" type=\"bottom\" class=\"tc\">\r\n\t\t\t<view class=\"tcnr\">\r\n\t\t\t\t<view class=\"close\" @click=\"closeSpecif\"></view>\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image :src=\"api + infor.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t<view class=\"title\">{{infor.name}}</view>\r\n\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t<view class=\"price\">￥{{infor.price}}</view>\r\n\t\t\t\t\t\t\t<view class=\"numb\">库存{{infor.stock}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buynum\">\r\n\t\t\t\t\t<view class=\"title\">购买数量</view>\r\n\t\t\t\t\t<view class=\"countBox\">\r\n\t\t\t\t\t\t<view class=\"carSub\" @click=\"changeNum(0)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon_sub1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"cartNum\">{{item.number}}</view> -->\r\n\t\t\t\t\t\t<input class=\"cartNum\" placeholder-style=\"color:#333\" type=\"number\" :value=\"number\" @input=\"getnumber\" @blur=\"blur\" />\r\n\t\t\t\t\t\t<view class=\"carAdd\" @click=\"changeNum(1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon_add1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t<view @click=\"getSubmit\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\timport { mapState, mapMutations} from 'vuex'\r\n\timport parse from 'mini-html-parser2'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\tnavHeight: this.$cache.fetchCache('navHeight'),\r\n\t\t\t\tstatusBarHeight: this.$cache.fetchCache('statusBarHeight'),\r\n\t\t\t\theaderHeight: this.$cache.fetchCache('headerHeight'),\r\n\t\t\t\tbackgroud: 'rgba(255,255,255,0)',\r\n\t\t\t\tshow: false,\r\n\t\t\t\ttime: '.1s',\r\n\t\t\t\t\r\n\t\t\t\ttype: '',// 1加入购物车 2购买\r\n\t\t\t\tid:'',// 商品ID\r\n\t\t\t\tinfor: [],// 详情\r\n\t\t\t\t\r\n\t\t\t\tbanner: [],\r\n\t\t\t\t\r\n\t\t\t\tnumber: 1,// 购买数量\r\n\t\t\t\t\r\n\t\t\t\tcarNumb: '',// 购物车数量\r\n\t\t\t\t\r\n\t\t\t\tactivities_tags: '',// 活动\r\n\t\t\t\tcontent: '',// 商品详情\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.id = e.id;\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.getInfor()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getCarnum()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 详情数据\r\n\t\t\tgetInfor(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tid: that.id,\r\n\t\t\t\t\ttype: 'goods',\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.goodsDetail(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.infor = data\r\n\t\t\t\t\t\tthat.banner = data.images\r\n\t\t\t\t\t\tparse(data.activities_tags, (err, nodesList) => {\r\n\t\t\t\t\t\t\tif(!err){\r\n\t\t\t\t\t\t\t\tthis.activities_tags = nodesList\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tparse(data.content, (err, nodesList) => {\r\n\t\t\t\t\t\t\tif(!err){\r\n\t\t\t\t\t\t\t\tthis.content = nodesList\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 购物车商品数量\r\n\t\t\tgetCarnum(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.carList(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.carNumb = res.data.data.length\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 首页\r\n\t\t\tgoHome(){\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 选择规格弹窗\r\n\t\t\topenSpecif(e) {\r\n\t\t\t\tif(!this.$cache.fetchCache('token')){\r\n\t\t\t\t\tthis.$util.toLogin()\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.type = e\r\n\t\t\t\t\tthis.$refs.specif.open()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 修改商品数量\r\n\t\t\tchangeNum(e){\r\n\t\t\t\tlet stock = this.infor.stock;// 库存\r\n\t\t\t\tlet number = parseInt(this.number);\r\n\t\t\t\tif (e == 0) {\r\n\t\t\t\t\tnumber = number - 1;\r\n\t\t\t\t\tif (number < 1) {\r\n\t\t\t\t\t\tthis.number = number = 1;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.number = number\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnumber = number + 1;\r\n\t\t\t\t\tif(number > stock){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '已到最大库存',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.number = number = stock\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.number = number\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t// 输入数量\r\n\t\t\tgetnumber(e){\r\n\t\t\t\tlet value = e.detail.value;\r\n\t\t\t\tlet stock = this.infor.stock;// 库存\r\n\t\t\t\tif(value == ''){\r\n\t\t\t\t\tthis.number = value = 0\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(value > stock){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '已到最大库存',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.number = value = stock\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.number = value\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 离开数量框\r\n\t\t\tblur(){\r\n\t\t\t\tlet stock = this.infor.stock;// 库存\r\n\t\t\t\tif(this.number == ''){\r\n\t\t\t\t\tthis.number = 0\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.number > stock){\r\n\t\t\t\t\t\tthis.number = stock\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 购物车\r\n\t\t\tgetSubmit(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(that.number == 0){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '请选择购买数量',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t// 加入购物车\r\n\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\tgoods_list: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"goods_id\": that.id,\r\n\t\t\t\t\t\t\t\t\t\"goods_num\": that.number,\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthat.$api.Addcar(params).then(res => {\r\n\t\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\ttitle: '加入购物车成功',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthat.$refs.specif.close()\r\n\t\t\t\t\t\t\t\tthat.getCarnum()\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\ttitle: '加入购物车失败',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t// 购买\r\n\t\t\t\t\t\tlet goodsList = [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"id\": that.id,\r\n\t\t\t\t\t\t\t\t\"price\": that.infor.price,\r\n\t\t\t\t\t\t\t\t\"num\": that.number,\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t\tthat.$refs.specif.close()\r\n\t\t\t\t\t\tthat.$cache.updateCache('goodsList', goodsList);\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl:'order?type=1'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 购物车\r\n\t\t\tgoCar(){\r\n\t\t\t\tif(!this.$cache.fetchCache('token')){\r\n\t\t\t\t\tthis.$util.toLogin()\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesCar/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t// 关闭规则弹窗\r\n\t\t\tcloseSpecif(){\r\n\t\t\t\tthis.$refs.specif.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tback() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tonPageScroll(e) {\r\n\t\t\t\tif (e.scrollTop) {\r\n\t\t\t\t\tthis.backgroud = `rgba(255, 255, 255,${e.scrollTop / (e.scrollTop + 43.88)})`;\r\n\t\t\t\t} else if (e.scrollTop || e.scrollTop < 5) { // 这里<5 防止监听0 失败\r\n\t\t\t\t\tthis.backgroud = 'transparent'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.uni-navbar__header {\r\n\t\tbackground: transparent;\r\n\t}\r\n\t.content{\r\n\t\tbackground: #fff;\r\n\t\tmin-height: auto;\r\n\t}\r\n\t.banner_bg .banner {\r\n\t\twidth: 100%;\r\n\t\theight: 740rpx;\r\n\t}\r\n\t.banner_bg .banner .swiper_image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.goods{\r\n\t\tpadding-bottom: 100rpx;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\t.goods .money{\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat center;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tpadding: 14rpx 20rpx 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.goods .money .num{\r\n\t\tfont-size: 38rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t.goods .money .text{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.goods .money .text .price{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\t color: #fff;\r\n\t\t text-decoration: line-through;\r\n\t}\r\n\t.goods .money .text text{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\t color: #fff;\r\n\t\t margin: 0 10rpx;\r\n\t}\r\n\t.goods .money .text .sold{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\t color: #fff;\r\n\t}\r\n\t\r\n\t.goods .gname{\r\n\t\tpadding: 20rpx 24rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t}\r\n\t.goods .activity{\r\n\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t\tpadding: 26rpx 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t}\r\n\t.goods .activity image{\r\n\t\twidth: 50rpx;\r\n\t\theight: 49rpx;\r\n\t}\r\n\t.goods .activity .bt{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\t.goods .activity .xx{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.goods .details{\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t.goods .details .title{\r\n\t\ttext-align: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.goods .details .xx image{\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t}\r\n\t\r\n\t\r\n\t.foot{\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 15rpx 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.foot .nav{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.foot .nav .xx{\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t\tmargin-right: 40rpx;\r\n\t}\r\n\t.foot .nav .xx:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.foot .nav .xx image{\r\n\t\twidth: 44rpx;\r\n\t\theight: auto;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t.foot .nav .xx .name{\r\n\t\tfont-size: 22rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.foot .nav .xx .car{\r\n\t\tfont-size: 22rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.foot .nav .xx .num{\r\n\t\twidth: 26rpx;\r\n\t\theight: 26rpx;\r\n\t\tbackground: #FF0000;\r\n\t\tborder-radius: 50%;\r\n\t\ttext-align: center;\r\n\t\tposition: absolute;\r\n\t\ttop: -17rpx;\r\n\t\tright: 0;\r\n\t}\r\n\t.foot .nav .xx .num text{\r\n\t\tfont-size: 20rpx;\r\n\t\tline-height: 26rpx;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: block;\r\n\t\ttransform: scale(.8);\r\n\t}\r\n\t.foot .btn{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.foot .btn .add{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #3478FB;\r\n\t\tborder: 1rpx solid #3478FB;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 70rpx;\r\n\t\twidth: 210rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t.foot .btn .pay{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder: 1rpx solid #3478FB;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 70rpx;\r\n\t\twidth: 210rpx;\r\n\t}\r\n\t\r\n\t.tcnr{\r\n\t\tposition: relative;\r\n\t\t/*  #ifdef  MP-ALIPAY  */\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\t/*  #endif  */\r\n\t\t\r\n\t\tbackground: #fff;\r\n\t\tpadding: 70rpx 20rpx 46rpx;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\theight: 672rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.tcnr .close{\r\n\t\tposition: absolute;\r\n\t\tright: 0rpx;\r\n\t\ttop: 0;\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAq1JREFUSEvFls1q1VAQx/9zSLLyAVy4EKwoiGChUHtzgw0UWne6EAQV61sIulTsW1hRwYWgC8EWK6kkuSlVUKELxbor6AO4kCQ9I3NJSkzzde8FzTJnZn5n5swXocM3GAwuaK2XAPQBTBHRUVFj5p8AdgEESqm1Xq/3rs0c1Ql4nmeYpnmNiO4KpM1Qdr7LzPeSJHnqum5apVMJDMPwBIBnAGY6gspiHwBctW37e/ngEHAwGCxqrZ8T0ZExYUM1Zv6llLrU6/XeFu38Bcxgr4jImASW6zJzqpRaKkIPgJ7nTZmm+XFSz8oXFU+TJJl2XVeSC0OgJIhlWdEEb9YWkK04jh1JpCEwiqKbWuvVNq1JzpVSy3Nzc4+GwDAMvzWk/jSAlJnf5PVXFTZmvgzgt1LKr7nYrm3bJ8n3/XmllFd3e8Mwzs3Ozn72PO+0aZpeGSrFz8xXHMcJtre3zyRJslNnS2vtUhAED4jodp1Q1k3m+/3+1zJUzpIkcV3X/RIEwSkAm3VRyEplRYA+EUnLqv2qoCI8CiwzHgjwR9OtCjUlffPAU/nf1bOCJ3sUhiF3zb6ip6LTJYyHWtuowDyMWf1WJlKTAyOFtPRmqEqklmjtdU6acoJkhiuztwE6TJrWsshhUmdxHG+UBnBnKDOvSOH3G7qDXPasbds7URTZ+/v7a+XmXkykIAhmiOh9Y+G3tTYimgdgaK1f1k0SgVqWtcDMRpqmnxpbWwZcBvCwa3mMKXfLtu3V/zOespr6dwM4D5Hv+wtKqdfyZmOGrayWaq0vOo6zkR8cWqIESkQvJl01ZLWQGVmECbRyTZT9xrKsxwDOj+mprBQ38j2maKNxEbYs6zqAO6MswgDux3H8ZKRFuOyVbAVEtJjNzeMAjmUye8ws29gWM687jrPZFpE/hImlCD814eIAAAAASUVORK5CYII=) no-repeat center;\r\n\t\tbackground-size: 28rpx 28rpx;\r\n\t}\r\n\t.tcnr .nr{\r\n\t\tpadding: 0 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 110rpx;\r\n\t}\r\n\t.tcnr .nr .pic{\r\n\t\twidth: 148rpx;\r\n\t\theight: 148rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t.tcnr .nr .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.tcnr .nr .xx{\r\n\t\twidth: calc(100% - 172rpx);\r\n\t}\r\n\t.tcnr .nr .xx .title{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.tcnr .nr .xx .money{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.tcnr .nr .xx .money .price{\r\n\t\tfont-size: 38rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.tcnr .nr .xx .money .numb{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.tcnr .buynum{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 160rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t.tcnr .buynum .title{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.tcnr .buynum .countBox{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t}\r\n\t.tcnr .buynum .countBox view{\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t}\r\n\t.tcnr .buynum .countBox view image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.tcnr .buynum .countBox .cartNum{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tmin-height: 36rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\twidth: 54rpx;\r\n\t\tmargin: 0 10rpx;\r\n\t}\r\n\t.tcnr .btn{\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.tcnr .btn view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 40rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods_details.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods_details.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974003\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}