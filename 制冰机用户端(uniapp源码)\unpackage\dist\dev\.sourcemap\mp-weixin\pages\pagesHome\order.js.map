{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?921f", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?0c88", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?a19e", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?d2c5", "uni-app:///pages/pagesHome/order.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?1829", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order.vue?7e9d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "type", "address", "note", "list", "total_amount", "pay_amount", "total_num", "coupons", "coupons_cont", "exchange_coupon_amount", "onLoad", "onShow", "uni", "that", "methods", "get<PERSON><PERSON><PERSON>", "res", "code", "msg", "icon", "title", "duration", "catch", "getorderConfirm", "order_type", "goods_list", "dispatch_address_id", "user_coupons_id", "from", "go<PERSON>dd<PERSON>", "url", "openCoupons", "closeSpecif", "getCoupons", "console", "getCoupon", "goPay", "mask", "memo", "user_dispatch_address_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoFzuB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;;MAEAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAF;QACA,gBAIAG;UAHAC;UACAnB;UACAoB;QAEA;UACA;UACAf;YACA;cACAU;YACA;UACA;QACA;UACAD;YACAO;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAJ;MACA;MACAX;QACA,iBAIAG;UAHAC;UACAnB;UACAoB;QAEA;UACAL;UACAA;UACAA;UACAA;UACAA;UACAA;YACA;UACA;QACA;UACAD;YACAO;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IAEA;IACAO;MACAjB;QACAkB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAxB;UACAO;UACAC;UACAC;QACA;QACA;MACA;MACAT;QACAQ;QACAiB;MACA;MACA;MACA;MACA;QACAb;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;QACAc;QACAb;QACAc;QACAZ;QACAC;QACAJ;MACA;;MACAX;QACA,iBAIAG;UAHAC;UACAnB;UACAoB;QAEA;UACAN;UACA;UACA;YACAA;cACAkB;YACA;UACA;YACAlB;cACAkB;YACA;UACA;QACA;UACAlB;UACAA;YACAO;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/RA;AAAA;AAAA;AAAA;AAAshC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA1iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=8350c6ce&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=template&id=8350c6ce&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.type != 2 && _vm.coupons.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"order\">\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<image src=\"/static/icon_address.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"address\" @click=\"goAddress\">\r\n\t\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t\t<view>{{address?address.consignee:'请选择收货地址'}}</view>\r\n\t\t\t\t\t\t<view v-if=\"address\">{{address.phone}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xx\" v-if=\"address\">{{address.province_name + address.city_name + address.area_name + address.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"goods\">\r\n\t\t\t\t<view class=\"nr\" v-for=\"(item,index) in list\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image :src='api + item.image' mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t<view class=\"gname\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"numb\">\r\n\t\t\t\t\t\t\t<view class=\"price\" v-if=\"type == 2\">{{item.price}}积分</view>\r\n\t\t\t\t\t\t\t<view class=\"price\" v-else>￥{{item.price}}</view>\r\n\t\t\t\t\t\t\t<view class=\"num\">x{{item.num}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"note\">\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view>备注</view>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"建议留言前先于卖家沟通确认\" placeholder-style=\"color:#999\" v-model=\"note\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view>商品金额</view>\r\n\t\t\t\t\t<text v-if=\"type == 2\">{{total_amount}}积分</text>\r\n\t\t\t\t\t<text v-else>￥{{total_amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"voucher\" v-if=\"types != 2 && exchange_coupon_amount > 0\">\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view>兑换券</view>\r\n\t\t\t\t\t<text>-￥ {{exchange_coupon_amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"securities\" v-if=\" type != 2 && coupons.length > 0\">\r\n\t\t\t\t<view class=\"nr\" @click=\"openCoupons\">\r\n\t\t\t\t\t<view>优惠券</view>\r\n\t\t\t\t\t<text>{{coupons_cont?coupons_cont.tag:'请选择优惠券'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"foot\">\r\n\t\t\t<view class=\"money\">\r\n\t\t\t\t<view class=\"num\">共{{total_num}}件</view>\r\n\t\t\t\t<view class=\"text\">合计：</view>\r\n\t\t\t\t<view class=\"price\" v-if=\"type == 2\">{{pay_amount}}积分</view>\r\n\t\t\t\t<view class=\"price\" v-else>￥{{pay_amount}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn\" :class=\"types == 1 && isDistribution == 1?'':'active'\" @click=\"goPay\">提交订单</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<uni-popup ref=\"coupons\" type=\"bottom\" class=\"tc\">\r\n\t\t\t<view class=\"tcnr\">\r\n\t\t\t\t<view class=\"close\" @click=\"closeSpecif\"></view>\r\n\t\t\t\t<view class=\"title\">选择优惠券</view>\r\n\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t<view class=\"nr\" @click=\"getCoupons('')\">\r\n\t\t\t\t\t\t<view class=\"check\" :class=\"coupons_cont == ''?'active':''\"></view>\r\n\t\t\t\t\t\t<view class=\"xx\">不使用优惠券</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"nr\" v-for=\"(item,index) in coupons\" @click=\"getCoupons(item)\">\r\n\t\t\t\t\t\t<view class=\"check\" :class=\"item.user_coupons_id == coupons_cont.user_coupons_id?'active':''\"></view>\r\n\t\t\t\t\t\t<view class=\"xx\">{{item.tag}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"getCoupon\">确定</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\ttype: '',// 1单商品 2积分商品 3商品购物车\r\n\t\t\t\t\r\n\t\t\t\taddress:'',// 地址\r\n\t\t\t\t\r\n\t\t\t\tnote:'',// 备注\r\n\t\t\t\t\r\n\t\t\t\tlist: [],// 商品数据\r\n\t\t\t\t\r\n\t\t\t\ttotal_amount: '',// 商品金额\r\n\t\t\t\tpay_amount: '',// 总价\r\n\t\t\t\ttotal_num: '',// 总件数\r\n\t\t\t\t\r\n\t\t\t\tcoupons: [],// 优惠券列表\r\n\t\t\t\tcoupons_cont: '',// 优惠券信息\r\n\t\t\t\t\r\n\t\t\t\texchange_coupon_amount: '',// 兑换券\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.type = options.type\r\n\t\t\tthis.getorderConfirm()\r\n\t\t\tthis.getAddress()\r\n\t\t},\r\n\t\t\r\n\t\tonShow() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.$on('updateData',function(data){\r\n\t\t\t\tthat.address = data\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 地址\r\n\t\t\tgetAddress(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.Address(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tlet list = data\r\n\t\t\t\t\t\tlist.forEach(el => {\r\n\t\t\t\t\t\t\tif(el.is_default == 1){\r\n\t\t\t\t\t\t\t\tthat.address = el\r\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 确认订单信息\r\n\t\t\tgetorderConfirm(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet order_type = ''\r\n\t\t\t\tif(that.type == 2){\r\n\t\t\t\t\torder_type = 'score'\r\n\t\t\t\t}else{\r\n\t\t\t\t\torder_type = 'goods'\r\n\t\t\t\t}\r\n\t\t\t\tlet goods_list = that.$cache.fetchCache('goodsList');\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tgoods_list: goods_list,\r\n\t\t\t\t\tdispatch_address_id: that.address.id,\r\n\t\t\t\t\tuser_coupons_id: that.coupons_cont?that.coupons_cont.user_coupons_id:'',\r\n\t\t\t\t\tfrom: that.type == 3?'cart':'',\r\n\t\t\t\t\torder_type: order_type,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.orderConfirm(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.list = data.goods_list\r\n\t\t\t\t\t\tthat.total_amount = data.total_amount\r\n\t\t\t\t\t\tthat.pay_amount = data.pay_amount\r\n\t\t\t\t\t\tthat.coupons = data.coupons\r\n\t\t\t\t\t\tthat.exchange_coupon_amount = data.exchange_coupon_amount\r\n\t\t\t\t\t\tthat.total_num = data.goods_list.reduce((prev, next) => {\r\n\t\t\t\t\t\t\treturn prev + next.num;\r\n\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 地址选择\r\n\t\t\tgoAddress(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'address?type=2'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择优惠券弹窗\r\n\t\t\topenCoupons() {\r\n\t\t\t\tthis.$refs.coupons.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭优惠券弹窗\r\n\t\t\tcloseSpecif(){\r\n\t\t\t\tthis.$refs.coupons.close()\r\n\t\t\t\tthis.coupons_cont = ''\r\n\t\t\t},\r\n\t\t\t// 选择优惠券\r\n\t\t\tgetCoupons(e){\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tthis.coupons_cont = e\r\n\t\t\t},\r\n\t\t\tgetCoupon(){\r\n\t\t\t\tthis.$refs.coupons.close()\r\n\t\t\t\tthis.getorderConfirm()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交订单\r\n\t\t\tgoPay(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(!that.address){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: \"请选择收件地址！\",\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '生成订单......',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t});\r\n\t\t\t\tlet goods_list = that.$cache.fetchCache('goodsList');\r\n\t\t\t\tlet order_type = ''\r\n\t\t\t\tif(that.type == 2){\r\n\t\t\t\t\torder_type = 'score'\r\n\t\t\t\t}else{\r\n\t\t\t\t\torder_type = 'goods'\r\n\t\t\t\t}\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\t// username: that.name,\r\n\t\t\t\t\t// mobile: that.phone,\r\n\t\t\t\t\t// address: that.address,\r\n\t\t\t\t\tmemo: that.note,\r\n\t\t\t\t\tgoods_list: goods_list,\r\n\t\t\t\t\tuser_dispatch_address_id: that.address.id,\r\n\t\t\t\t\tuser_coupons_id: that.coupons_cont?that.coupons_cont.user_coupons_id:'',\r\n\t\t\t\t\tfrom: that.type == 3?'cart':'',\r\n\t\t\t\t\torder_type: order_type, //goods 商城购物,score积分订单，gift送心意\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.createOrder(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet order_sn = data.order_sn\r\n\t\t\t\t\t\tif(that.type == 2){\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/pagesPay/index?type=3' + '&order_sn=' + order_sn\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/pagesPay/index?type=2' + '&order_sn=' + order_sn\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.order{\r\n\t\tpadding: 20rpx 24rpx 120rpx;\r\n\t}\r\n\t.order .top{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 28rpx 34rpx 45rpx 22rpx;\r\n\t\tbackground: #fff url(data:image/png;base64,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) no-repeat center;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .top image{\r\n\t\twidth: 64rpx;\r\n\t\theight: auto;\r\n\t}\r\n\t.order .top .address{\r\n\t\twidth: calc(100% - 88rpx);\r\n\t\tpadding-right: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAQpJREFUWEft1jEOgjAUBuBXTuEhvEBDGEgcXDyAN3Aw0Rvo4K6bl/AAbkwFnL2EB3CkPNMEErb29T0kJrKW8H/9A48qmPhSE+fDH/A7DdR1vbPWnpIkWWqtjdS7E9SAMWaulHq6UER8SyKCAC64LMszAOylEcGAsRAkwBgIMkAaEQWQREQDpBAsgASCDeAiRAAd4goAm8GcWGitH76JKQaoquqAiMcusLHWrrIsu38F4P4Tbdte+nBEXKdpevOFu3V2A5xwNoAbzgJIhEcDpMKjAJLhZIB0OAkwRngwYHgkA4CG8p37ZkHwHOiOZFvJ8OAG+l0URTHL8/zl2xVlPbgBykMp9/4BkzfwAXHt4yGr8bP0AAAAAElFTkSuQmCC) no-repeat right center;\r\n\t\tbackground-size: 32rpx auto;\r\n\t}\r\n\t.order .top .address .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.order .top .address .nr view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\tmargin-right: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .top .address .nr view:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.order .top .address .xx{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\t\r\n\t.order .goods{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx 24rpx 35rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .goods .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t.order .goods .nr:last-child{\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t.order .goods .nr .pic{\r\n\t\twidth: 148rpx;\r\n\t\theight: 148rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t.order .goods .nr .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.order .goods .nr .xx{\r\n\t\twidth: calc(100% - 168rpx);\r\n\t}\r\n\t.order .goods .nr .xx .gname{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .goods .nr .xx .numb{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.order .goods .nr .xx .numb .price{\r\n\t\tfont-size: 36rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .goods .nr .xx .numb .num{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .note{\r\n\t\tbackground: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t}\r\n\t.order .note .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t}\r\n\t.order .note .xx:last-child{\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t.order .note .xx view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.order .note .xx input{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\twidth: 80%;\r\n\t\ttext-align: left;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.order .note .xx text{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .voucher{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t.order .voucher .nr{\r\n\t\tpadding: 0 22rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t}\r\n\t.order .voucher .nr view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .voucher .nr text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #FC451E;\r\n\t}\r\n\t.order .securities{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t.order .securities .nr{\r\n\t\tpadding: 0 22rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t}\r\n\t.order .securities .nr:last-child{\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t.order .securities .nr view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .securities .nr text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #FC451E;\r\n\t\tpadding-right: 22rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAZCAYAAAABmx/yAAAAAXNSR0IArs4c6QAAAeRJREFUOE+N0r+LE0EUB/DvWwQLCRZa2dpoIUpmZkmvXfoI54nVYcTTg/PsLAKWEg9/cid31Smi2KaxCYjixX2JpA7Zf8BCYpvcPJllNuzd7eZ22nmf+T7ePGLmDQBPALS11k9R8hAz/wNQcfVEtKmUWi9jHXwJ4EFaLCLPtdYbRCSLHiB3GUXRHhEtZwrbSqnHi3AC3WHmdwBWMsmvtdZrRGTzkufQXfb7/S0RuZspfBvH8cNGo3FwFB+Cvu1tInLJyR0RvRmPx2tH8THo237hB5be7yilmkQ0T86FHrtpr6bJAHbiOG6myYXQt/2KiO5n8K5SasVNeyH0yc8APEqxiLzvdDp3ToQ+uU1E2Y3aLQU9/k1E1/y3/C0FmfkngFpmOVYXwlarFdTr9X0iMhm0bozZLITdbvdUpVIZALiSQU1jzHayGHl7OBqNTk8mk6gI5cLhcHhmOp3+AHA18+g9rfVWNuRQIjOfBbAP4FJaFATBUrVa/Vi45L1e71wQBN+JKEUiIreMMcfQvFVmPg/gG4DL/mUhottKqQ95M0jgYDC4YK116KIvsiJy0xjzpQglMIqiX+k/iciBiCyFYfh5EUogM/8B4FqdWWuXwzD8dBJKW71urb0xm826tVrtaxnkav4DqX3HuvoI+RYAAAAASUVORK5CYII=) no-repeat right center;\r\n\t\tbackground-size: 14rpx 25rpx;\r\n\t}\r\n\t\r\n\t\r\n\t.foot{\r\n\t\twidth: 100%;\r\n\t\tpadding: 15rpx 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tz-index: 2;\r\n\t}\r\n\t.foot .money{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.foot .money .num{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.foot .money .text{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 22rpx;\r\n\t}\r\n\t.foot .money .price{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.foot .btn{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #C5C5C5;\r\n\t\tborder-radius: 40rpx;\r\n\t\tpadding: 0 40rpx;\r\n\t}\r\n\t.foot .btn.active{\r\n\t\tbackground: #3478FB;\r\n\t}\r\n\t\r\n\t\r\n\t.tcnr{\r\n\t\tposition: relative;\r\n\t\t/*  #ifdef  MP-ALIPAY  */\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\t/*  #endif  */\r\n\t\t\r\n\t\tbackground: #fff;\r\n\t\tpadding: 40rpx 30rpx 46rpx;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.tcnr .close{\r\n\t\tposition: absolute;\r\n\t\tright: 0rpx;\r\n\t\ttop: 0;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAq1JREFUSEvFls1q1VAQx/9zSLLyAVy4EKwoiGChUHtzgw0UWne6EAQV61sIulTsW1hRwYWgC8EWK6kkuSlVUKELxbor6AO4kCQ9I3NJSkzzde8FzTJnZn5n5swXocM3GAwuaK2XAPQBTBHRUVFj5p8AdgEESqm1Xq/3rs0c1Ql4nmeYpnmNiO4KpM1Qdr7LzPeSJHnqum5apVMJDMPwBIBnAGY6gspiHwBctW37e/ngEHAwGCxqrZ8T0ZExYUM1Zv6llLrU6/XeFu38Bcxgr4jImASW6zJzqpRaKkIPgJ7nTZmm+XFSz8oXFU+TJJl2XVeSC0OgJIhlWdEEb9YWkK04jh1JpCEwiqKbWuvVNq1JzpVSy3Nzc4+GwDAMvzWk/jSAlJnf5PVXFTZmvgzgt1LKr7nYrm3bJ8n3/XmllFd3e8Mwzs3Ozn72PO+0aZpeGSrFz8xXHMcJtre3zyRJslNnS2vtUhAED4jodp1Q1k3m+/3+1zJUzpIkcV3X/RIEwSkAm3VRyEplRYA+EUnLqv2qoCI8CiwzHgjwR9OtCjUlffPAU/nf1bOCJ3sUhiF3zb6ip6LTJYyHWtuowDyMWf1WJlKTAyOFtPRmqEqklmjtdU6acoJkhiuztwE6TJrWsshhUmdxHG+UBnBnKDOvSOH3G7qDXPasbds7URTZ+/v7a+XmXkykIAhmiOh9Y+G3tTYimgdgaK1f1k0SgVqWtcDMRpqmnxpbWwZcBvCwa3mMKXfLtu3V/zOespr6dwM4D5Hv+wtKqdfyZmOGrayWaq0vOo6zkR8cWqIESkQvJl01ZLWQGVmECbRyTZT9xrKsxwDOj+mprBQ38j2maKNxEbYs6zqAO6MswgDux3H8ZKRFuOyVbAVEtJjNzeMAjmUye8ws29gWM687jrPZFpE/hImlCD814eIAAAAASUVORK5CYII=) no-repeat center;\r\n\t\tbackground-size: 28rpx 28rpx;\r\n\t}\r\n\t.tcnr .title{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 65rpx;\r\n\t}\r\n\t.tcnr .list{\r\n\t\theight: 400rpx;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\t.tcnr .list .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.tcnr .list .nr:last-child{\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t.tcnr .list .nr .check{\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA2lJREFUWEfNV01oXFUUPucmeVCMEFzVlZXaSgloU2QmeedkU5TqSoJQ3VQQ7KatG6slKKK01LZUI90odKMLRaRCFv4uiovmnjc/i462ZGVtKqW2u1Asrcybeaec6UyYTKfNm3lp44PhDtxzvu+79557zzkIKT9VxXK5vCmO460AMAYANtrPvt8BoGLj4OBgJZ/PX0BETQONaYxE5CMA2KOqi0binGuRGTHUarWtSZI0RCHimKoOA8AXzHxoJfz7Coii6ClVnVXVU86542EY3loJ0OajKFoHANNJkrw8NDQ0lc/nF+7ld08BInIAAN5U1Slmnk9D3GlTLBafqdVqs4j4ORF92g2jqwAROWMLIaLpfog7fbz3x+1oiOj5zrm7BDTJTxLR16tB3sIQkTdUdRczb2/HXSague2PrdbKOxcgIp8AwD9ENNOaWxLQDLifiWjzaq68y3FcHBgY2D4xMXHJ5pYEiMh5VX2t34BLK3pubu5Z59xXRGTX9o4Au+eqmjDzwbRAWexExN6H/4joMNoLF0XRNUTckPaeZyE33/n5+eHFxcU/mflxLJVKm+M4/oGZn84K3Iu/iFwAgB3ovd8JAK8w86u9AGS1FZFTAPCdCTjinLsehuHRrKC9+Hvv30PER0zAL865E2EY/toLQFZb7/1LAPCWCbgaBMFYLpe7lhW0F/9yuby+Wq1W/hcC1vwIPgaAf5n5SC9bmNW2PQjX6hp+DwDfYrFY3FSv13960EmoS1L6S1VfaDzFInLFObfxYT3F3vtH7UUmoicaych7/4FzbjAMww+znm0afxE5rKo3LO6W0rH33krq18fHx8+lAenXplAobKvX6yeZ+bll9UCpVHoyjuPTzLyxX/A0ft77v4Mg4Fwud3mZgGZdsF9V1zPzu2nAerXx3n/mnLsUhuGJlm+3ovQ0AHxDRF/2SnA/exHZbVmXiF5st+talnvvf0PEs0T0zmqIsJUj4pZO8ruOoJ1MRN5W1X3WmExOTv7Rj5BmwM0652bat33FHWgZFAqFDUmSzALAjyMjI8dGR0dvpBFi9xwRp1V1RxAEU62A6+abtjl9X1X3IuJN64JV1ZrTShAENkK1WrUKdwwRt6mq/R9qNqcr5pdUAlrKRcSuaIOsSdQorU0MIpqYs0mSVJh5IW17fhu6dI9iqNFKkgAAAABJRU5ErkJggg==) no-repeat center;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\t.tcnr .list .nr .check.active{\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAAAXNSR0IArs4c6QAAA+BJREFUWEfFmGuIVGUYx///c86cdXMbSXcDqciki2Ds7Qz0pYLUoiBDv5jFopa0QVHZRc/sFkQQ28zKgisVFnQxKoMiFxRSTM0PfUg7s65kkpEgQkHtRruVMXMuT5wZZ/Y2Z87ZmbHeb4f3ef7P733e63OIObTlT0hTw5XuXaSsJHALgGsBXHdJ4gLACwI5K8LD2T/Vo6ff5F9R5RnF0OjJ3UaPLwuwikQsio8IbAJfiqK9Yr3Gb8J8KoIkeuVWcZ1XATxAIhL0zIAiEBBD4mkvZvp5JggoULzTzHaRyjsE9LDRROkXICfibc6kGz4sZ18WxDDtJIC+arMQBFbIjrLVSqkDM21mgRg97tMUbzDKKKu1EZEtVlqfFmMaSGfSXqEABwFo1QaJ6Oe4Hu4d7o8dLtqXQNqevXhNTI+dArEwoliNZhxzvWzrcP/8n32hEohh5t4jualG9Tm6y7vfpvTNJZA2M7tco3KKgDJHpVrNHVu09pE0T+czYpjOXlLW1Kpajb+An1opbR2NbrmCC50xAPOqEQryaZoHdN2hwDonOPGTVJK+KL9rLTS2ZddQUfbWE2JRE7CrW8WSFsJxgfWDDs7/FhxBPG8tE9uct6BId71ApkL4mq4HPLgjBATcRSNpHyRwTz1AykH07nFx5LuKU+OHPuCDnCGwLAzkpsUFix9/KW9ZAwQE+J6JpD0OIF4J5PZlxPYuNW8y+IWLT76ePsJaIHxNEfwRCeSxlQq6V00eMQP7J2FqhbiUgAkf5AcAN1fKSKMO7HxERfuSyavJhzk0IqXdUVyYEdfEzHBnmTDtYyDuDFsj5WBGJwTN8QKcvzuqhPCn5isaSWc3IRvCQPz+cjC1QuTXCPg+O8zsepXKniggRZjXH1XRen0hE54APR9H2qKBIVzxHuKNT43FF8yP/0qgISpMvBF4+3EVS68mUkMuPj8eek4ESvtPyPG/J1r+/0tPOGSltbV5kPatuXZVpfVfPwME8BzxWkfSDYVngN8MM/cRyYejTk897ETkAyutb/S1SiCtL/xzg65pwwAW1CNIuAbHXdtuGx5oPD8NxP/oMJ37VMg+EIXz/PI1xwNWZ1KxA8UQs8sJ036OxKy6o55M4skzVr++c6pm2QKr08w9qZA7LkNZ4XgiWzJp/Y2ZAwsuOZP2CgKfEbiqTtkY84B1mVTsSDm9ioW10SuL4dp9IDdUvbXFv4a4m6r6ktXHgNfMlF1TadSJZK4DYB8Ed0deyAIXxCHHlZ6T2/WTYVmd068G43lpFtVerZD3A1gKchFEmvNByFGI+NXAOU9kP93YPmuAo2EAxf5/AU6Eibh+lv02AAAAAElFTkSuQmCC) no-repeat center;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\t.tcnr .list .nr .xx{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 32rpx;\r\n\t\tcolor: #636363;\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\t.tcnr .btn{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground: #3478FB;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974069\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}