/**
 * 全局请求接口列表
 * Created by gaobo on 2020/08/08
 * 
 */
import http from "./http.js"
// 接口
export default{
	
	// 微信授权
	codeWechat(params){
		return http.post("/api/wechat/oauth",params)
	},
	// 微信登录
	getToken(params){
		return http.post("/api/wechat/login",params)
	},
	// 支付宝授权
	codeAlipay(params){
		return http.post("/api/alipay/oauth",params)
	},
	
	
	// 判断绑定是否手机号
	checkBindMobile(params){
		return http.post("/api/user/isBindMobile",params)
	},
	// 绑定手机号
	getPhone(params){
		return http.post("/api/user/bindMobile",params)
	},
	// 支付宝绑定手机号
	getALPhone(params){
		return http.post("/api/alipay/bindMobile",params)
	},
	
	// 会员信息
	userInfo(params){
		return http.get("/api/shop/user/index",params)
	},
	// 年龄段选择
	ageList(params){
		return http.post("/api/index/age_list",params)
	},
	// 修改信息
	modifyUser(params){
		return http.post("/api/user/updateUser",params)
	},
	// 查看业务员，商家申请状态
	userStatus(params){
		return http.post("/api/user/apply_status",params)
	},
	// 业务员申请/商家申请
	apply(params){
		return http.post("/api/user/apply",params)
	},
	// 业务员收益明细
	machinProfitDetail(params){
		return http.post("/api/shop/profit/machinProfitDetail",params)
	},
	
	// 客服
	tel(params){
		return http.post("/api/index/tel",params)
	},
	
	// 柜子接口
	// 附近的制冰机
	near(params){
		return http.post("/api/index/near",params)
	},
	
	// 扫码首页
	machineindex(params){
		return http.get("/api/machine/index",params)
	},
	// 商品分类
	category(params){
		return http.get("/api/machine/index/category",params)
	},
	// 商品列表
	machineList(params){
		return http.get("/api/machine/index/goods",params)
	},
	// 添加购物车
	machineAdd(params){
		return http.post("/api/machine/cart/add",params)
	},
	// 修改购物车
	machineEdit(params){
		return http.post("/api/machine/cart/edit",params)
	},
	// 购物车列表
	mcarList(params){
		return http.get("/api/machine/cart/list",params)
	},
	// 清空购物车列表
	mcarClear(params){
		return http.post("/api/machine/cart/clear",params)
	},
	// 创建订单
	createOrderM(params){
		return http.post("/api/machine/order/create",params)
	},
	// 创建外卖订单
	createDeliveryOrder(params){
		return http.post("/api/machine/order/create",params)
	},
	// 订单信息
	OrdercontM(params){
		return http.get("/api/machine/pay/index",params)
	},
	// 支付
	createPayM(params){
		return http.post("/api/machine/pay/create",params)
	},
	// 柜子支付成功广告
	machinepayBanner(params){
		return http.get("/api/machine/order/payAd",params)
	},
	// 绑定售货机订单
	bindOrder(params){
		return http.post("/api/machine/order/bind",params)
	},
	// 获取配送时间选项
	getDeliveryTime(params){
		return http.get("/api/machine/order/deliveryTime",params)
	},
	// 计算配送费用
	calculateDeliveryFee(params){
		return http.post("/api/machine/order/calculateDeliveryFee",params)
	},
	// 根据机器编号获取机器信息
	getMachineByNo(params){
		return http.get("/api/machine/info",params)
	},
	// 上下级关系绑定
	bindUser(params){
		return http.get("/api/user/bind",params)
	},
	
	
	// 商城接口
	// 首页数据
	shopindex(params){
		return http.get("/api/shop/index",params)
	},
	// 公告详情
	noticedetail(params){
		return http.get("/api/shop/notice/detail",params)
	},
	// 商品分类页
	categoryindex(params){
		return http.get("/api/shop/category/index",params)
	},
	// 商品列表
	goodsList(params){
		return http.get("/api/shop/goods/list",params)
	},
	// 商品详情
	goodsDetail(params){
		return http.get("/api/shop/goods/detail",params)
	},
	// 积分商品列表
	scoreList(params){
		return http.get("/api/shop/score_goods/list",params)
	},
	// 积分商品详情
	scoreDetail(params){
		return http.get("/api/shop/score_goods/detail",params)
	},
	// 积分说明
	score_text(params){
		return http.get("/api/shop/index/score_text",params)
	},
	
	// 加入购物车
	Addcar(params){
		return http.post("/api/shop/cart/add",params)
	},
	// 修改购物车
	Editcar(params){
		return http.post("/api/shop/cart/edit",params)
	},
	// 购物车
	carList(params){
		return http.get("/api/shop/cart/list",params)
	},
	// 订单确认
	orderConfirm(params){
		return http.post("/api/shop/order/confirm",params)
	},
	// 创建订单
	createOrder(params){
		return http.post("/api/shop/order/create",params)
	},
	// 订单信息
	Ordercont(params){
		return http.get("/api/shop/pay/index",params)
	},
	// 支付
	createPay(params){
		return http.post("/api/shop/pay/create",params)
	},
	// 商城支付成功广告
	shoppayBanner(params){
		return http.get("/api/shop/order/payAd",params)
	},
	
	// 收货地址列表
	Address(params){
		return http.post("/api/shop/address/index",params)
	},
	// 删除地址
	delAddress(params){
		return http.post("/api/shop/address/del",params)
	},
	// 收货地址详情
	infoAddress(params){
		return http.get("/api/shop/address/info",params)
	},
	// 添加/修改收货地址
	editAddress(params){
		return http.post("/api/shop/address/edit",params)
	},
	
	
	// 逛
	// 分类列表
	strollList(params){
		return http.get("/api/shop/stroll/list",params)
	},
	// 产品列表
	strollGoods(params){
		return http.get("/api/shop/stroll/goods",params)
	},
	
	
	// 个人中心接口
	// 我的余额
	userMoney(params){
		return http.get("/api/shop/user/money",params)
	},
	// 充值列表
	rechargelist(params){
		return http.get("/api/shop/recharge/index",params)
	},
	// 创建充值订单
	createRecharge(params){
		return http.get("/api/shop/recharge/create",params)
	},
	
	// 支付成功弹窗
	payAfter(params){
		return http.get("/api/common/payAfter",params)
	},



	// 我的优惠券
	couponsList(params){
		return http.get("/api/shop/user/coupons",params)
	},
	// 兑换券
	voucherList(params){
		return http.get("/api/shop/user/exchangeCoupons",params)
	},
	// 兑换券获取记录
	voucherRecord(params){
		return http.get("/api/shop/user/exchangeCouponsLog",params)
	},
	// 积分
	score(params){
		return http.get("/api/shop/user/score",params)
	},
	
	
	// 业务员收益
	profitindex(params){
		return http.get("/api/shop/profit/index",params)
	},
	// 设备收益统计
	machine(params){
		return http.get("/api/shop/profit/machine",params)
	},
	// 设备交易明细
	machineProfitDetail(params){
		return http.get("/api/shop/profit/machineOrderDetail",params)
	},
	// 业务员提现首页
	withdrawal(params){
		return http.get("/api/shop/withdrawal/index",params)
	},
	// 业务员提现明细
	withdrawal_detail(params){
		return http.get("/api/shop/withdrawal/detail",params)
	},
	// 提现申请
	withdrawal_apply(params){
		return http.get("/api/shop/withdrawal/apply",params)
	},
	
	// 售货柜订单
	machineOrderList(params){
		return http.get("/api/shop/user/machineOrderList",params)
	},
	// 售货柜订单详情
	machineOrderDetail(params){
		return http.get("/api/shop/user/machineOrderDetail",params)
	},
	// 售货柜订单退款
	machineOrderRefund(params){
		return http.post("/api/shop/user/machineOrderRefund",params)
	},
	// 商城订单
	OrderList(params){
		return http.get("/api/shop/order/list",params)
	},
	// 商城订单详情
	OrderDetail(params){
		return http.get("/api/shop/order/detail",params)
	},
	// 取消订单
	cancel(params){
		return http.post("/api/shop/order/cancel",params)
	},
	// 确认收货
	complete(params){
		return http.post("/api/shop/order/complete",params)
	},
	
	// 送心意
	regard(params){
		return http.get("/api/shop/regard/index",params)
	},
	// 送心意详情
	regardDetails(params){
		return http.get("/api/shop/regard/detail",params)
	},
	// 领取兑换券
	regardReceive(params){
		return http.post("/api/shop/regard/receive",params)
	},
	// 领取优惠券
	couponReceive(params){
		return http.post("/api/shop/coupon/receive",params)
	},
	// 待领取优惠券列表
	getCoupon(params){
		return http.get("/api/shop/coupon/list",params)
	},
	
	
	// 上传
	AliyunOssSign(params){
		return http.post("/api/common/alioosSign",params)
	},
	// 留言反馈
	feedBack(params){
		return http.post("/api/feedback/add",params)
	},
	
	
	// 领取订单
	receive(params){
		return http.get("/api/shop/regard/receive",params)
	},
	
	
	// 首页轮播
	banner(params){
		return http.get("/api/machine/index/ad",params)
	},
	
	
	
	// 分销首页数据
	distribution(params){
		return http.get("/api/user/distribution_index",params)
	},
	// 分销收益查看
	d_earnings(params){
		return http.get("/api/user/distribution_log",params)
	},
	// 提现余额，提现手续费等
	w_info(params){
		return http.get("/api/shop/withdrawal/info",params)
	},
	
	
	// 提现是否打开
	switchs(params){
		return http.get("/api/index/switch",params)
	},
	
	
	
	// 省
	getArea(params){
		return http.post("/api/shop/address/area",params)
	},
	
	// 配送电话
	getPstel(params){
		return http.post("/api/index/pstel",params)
	},
	
	
}