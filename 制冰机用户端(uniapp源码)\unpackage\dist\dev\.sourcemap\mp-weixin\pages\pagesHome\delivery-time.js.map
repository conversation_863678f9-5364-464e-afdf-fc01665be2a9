{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?38f2", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?abcc", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?3c3c", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?b9ab", "uni-app:///pages/pagesHome/delivery-time.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?9138", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/delivery-time.vue?8d39"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "timeOptions", "selectedTime", "loading", "machine_no", "onLoad", "methods", "getDeliveryTimeOptions", "that", "params", "code", "msg", "uni", "icon", "title", "duration", "console", "selectTime", "date", "date_text", "start_time", "end_time", "text", "timestamp", "confirmSelection"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8CjvB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MAEA;MACA;QACAC;MACA;MAEAD;QACA;UAAAE;UAAAV;UAAAW;QACA;UACAH;QACA;UACAI;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAC;QACAJ;UACAC;UACAC;UACAC;QACA;MACA;QACAP;MACA;IACA;IAEA;IACAS;MAAA;MACA;MACA;QAAA;MAAA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACAZ;UACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACAH;;MAEA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAsjC,CAAgB,s+BAAG,EAAC,C;;;;;;;;;;;ACA1kC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/delivery-time.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/delivery-time.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./delivery-time.vue?vue&type=template&id=266dc751&scoped=true&\"\nvar renderjs\nimport script from \"./delivery-time.vue?vue&type=script&lang=js&\"\nexport * from \"./delivery-time.vue?vue&type=script&lang=js&\"\nimport style0 from \"./delivery-time.vue?vue&type=style&index=0&id=266dc751&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"266dc751\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/delivery-time.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./delivery-time.vue?vue&type=template&id=266dc751&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.timeOptions.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./delivery-time.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./delivery-time.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"content\">\n\t\t\t<!-- 时间选择列表 -->\n\t\t\t<view class=\"time-list\" v-if=\"timeOptions.length > 0\">\n\t\t\t\t<view class=\"date-section\" v-for=\"(dateItem, dateIndex) in timeOptions\" :key=\"dateIndex\">\n\t\t\t\t\t<view class=\"date-header\">{{dateItem.date_text}}</view>\n\t\t\t\t\t<view class=\"time-slots\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"time-slot\" \n\t\t\t\t\t\t\t:class=\"{'selected': selectedTime && selectedTime.date === dateItem.date && selectedTime.timestamp === slot.timestamp}\"\n\t\t\t\t\t\t\tv-for=\"(slot, slotIndex) in dateItem.slots\" \n\t\t\t\t\t\t\t:key=\"slotIndex\"\n\t\t\t\t\t\t\t@click=\"selectTime(dateItem.date, slot)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{{slot.text}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 空状态 -->\n\t\t\t<view class=\"empty-state\" v-else-if=\"!loading\">\n\t\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\n\t\t\t\t<text>暂无可选配送时间</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view class=\"loading-state\" v-if=\"loading\">\n\t\t\t\t<text>加载中...</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部确认按钮 -->\n\t\t<view class=\"bottom-bar\" v-if=\"selectedTime\">\n\t\t\t<view class=\"selected-info\">\n\t\t\t\t<text class=\"selected-text\">已选择：{{selectedTime.date_text}} {{selectedTime.text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"confirm-btn\" @click=\"confirmSelection\">\n\t\t\t\t确认\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttimeOptions: [], // 配送时间选项\n\t\t\t\tselectedTime: null, // 选中的时间\n\t\t\t\tloading: true,\n\t\t\t\tmachine_no: '' // 机器编号\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.machine_no) {\n\t\t\t\tthis.machine_no = options.machine_no\n\t\t\t}\n\t\t\tthis.getDeliveryTimeOptions()\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取配送时间选项\n\t\t\tgetDeliveryTimeOptions() {\n\t\t\t\tlet that = this\n\t\t\t\tthat.loading = true\n\t\t\t\t\n\t\t\t\tlet params = {}\n\t\t\t\tif (that.machine_no) {\n\t\t\t\t\tparams['machine-no'] = that.machine_no\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthat.$api.getDeliveryTime(params).then(res => {\n\t\t\t\t\tconst { code, data, msg } = res.data\n\t\t\t\t\tif (code === 1) {\n\t\t\t\t\t\tthat.timeOptions = data || []\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t\ttitle: msg || '获取配送时间失败',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('获取配送时间失败:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthat.loading = false\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 选择时间\n\t\t\tselectTime(date, slot) {\n\t\t\t\t// 格式化日期文本\n\t\t\t\tconst dateText = this.timeOptions.find(item => item.date === date)?.date_text || date\n\t\t\t\t\n\t\t\t\tthis.selectedTime = {\n\t\t\t\t\tdate: date,\n\t\t\t\t\tdate_text: dateText,\n\t\t\t\t\tstart_time: slot.start_time,\n\t\t\t\t\tend_time: slot.end_time,\n\t\t\t\t\ttext: slot.text,\n\t\t\t\t\ttimestamp: slot.timestamp\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 确认选择\n\t\t\tconfirmSelection() {\n\t\t\t\tif (!this.selectedTime) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '请选择配送时间',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 通过事件传递选中的时间\n\t\t\t\tuni.$emit('updateDeliveryTime', this.selectedTime)\n\t\t\t\t\n\t\t\t\t// 返回上一页\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tbackground: #f5f5f5;\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 120rpx;\n\t}\n\t\n\t.content {\n\t\tpadding: 20rpx;\n\t}\n\t\n\t/* 时间选择列表 */\n\t.time-list {\n\t\tbackground: #fff;\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t}\n\t\n\t.date-section {\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\t\n\t.date-section:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.date-header {\n\t\tpadding: 30rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\tbackground: #fafafa;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\t\n\t.time-slots {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tpadding: 20rpx;\n\t\tgap: 20rpx;\n\t}\n\t\n\t.time-slot {\n\t\tflex: 0 0 calc(33.333% - 14rpx);\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #f8f8f8;\n\t\tborder: 2rpx solid #f0f0f0;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.time-slot.selected {\n\t\tbackground: #4A90E2;\n\t\tborder-color: #4A90E2;\n\t\tcolor: #fff;\n\t}\n\t\n\t/* 空状态 */\n\t.empty-state {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 100rpx 0;\n\t\tcolor: #999;\n\t}\n\t\n\t.empty-state image {\n\t\twidth: 200rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t/* 加载状态 */\n\t.loading-state {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 100rpx 0;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 底部确认栏 */\n\t.bottom-bar {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n\t}\n\t\n\t.selected-info {\n\t\tflex: 1;\n\t}\n\t\n\t.selected-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.confirm-btn {\n\t\tbackground: #4A90E2;\n\t\tcolor: #fff;\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./delivery-time.vue?vue&type=style&index=0&id=266dc751&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./delivery-time.vue?vue&type=style&index=0&id=266dc751&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974072\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}