{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?37cc", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?27f2", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?313a", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?fe52", "uni-app:///pages/pagesHome/editor_address.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?1bf4", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/editor_address.vue?ff4e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "id", "consignee", "phone", "address", "is_default", "ssqData", "ssqRange", "ssqIndex", "ssqValue", "onLoad", "uni", "title", "methods", "getArea", "that", "res", "code", "msg", "icon", "duration", "catch", "ssqColumnChange", "column", "value", "ssqChange", "getSsqValue", "result", "getData", "province_index", "p", "city_index", "c", "area_index", "switchChange", "console", "getSubmit", "area_text"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8tB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+BlvB;EACAC;IACA;MACAC;MAAA;MACAC;MAEAC;MACAC;MAEAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;MACA;MACAD;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA,gBAIAC;UAHAC;UACAlB;UACAmB;QAEA;UACAH;UACA;YACAA,iBACAA,cACAA,sBACAA,6BACA;YACAA;UACA;;UACA;YACAA;UACA;QACA;UACAJ;YACAQ;YACAP;YACAQ;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;QAAAC;QAAAC;MACAT;MACA;MACA;QACA;QACAA;QACAA;MACA;QACA;QACAA;MACA;MACAA,iBACAA,cACAA,qCACAA,2DACA;IACA;IACA;IACAU;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAX;UACAY;QACA;MACA;MACAZ;IACA;IACA;IACAa;MACA;MACA;QACA3B;MACA;MACAc;QACA,iBAIAC;UAHAC;UACAlB;UACAmB;QAEA;UACA;UACA;UACA;UACAH;UACAA;UACAA;UACAA;UACAA;UACAA;YACA;YACA;cACAc;cACAd;cACAe;gBACA;gBACA;kBACAC;kBACAhB;kBACAiB;oBACA;oBACA;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;UACA;UACAlB;QACA;UACAJ;YACAQ;YACAP;YACAQ;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAa;MACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAGAC;MACA;MACA;QACAnC;QACAI;QACAH;QACAmC;QACAjC;QACAD;MACA;MACAY;QACA,iBAIAC;UAHAC;UACAlB;UACAmB;QAEA;UACAP;QACA;UACAA;YACAQ;YACAP;YACAQ;UACA;QACA;MACA,GACAC,sBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvOA;AAAA;AAAA;AAAA;AAA+hC,CAAgB,+8BAAG,EAAC,C;;;;;;;;;;;ACAnjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/editor_address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/editor_address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editor_address.vue?vue&type=template&id=0daa7867&\"\nvar renderjs\nimport script from \"./editor_address.vue?vue&type=script&lang=js&\"\nexport * from \"./editor_address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editor_address.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/editor_address.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editor_address.vue?vue&type=template&id=0daa7867&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editor_address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editor_address.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"form\">\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view class=\"xx\">收货人</view>\r\n\t\t\t\t<input type=\"text\" :placeholder=\"consignee?consignee:'请输入收货人'\" v-model=\"consignee\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view class=\"xx\">手机号</view>\r\n\t\t\t\t<input type=\"tel\" :placeholder=\"phone?phone:'请输入手机号'\" v-model=\"phone\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view class=\"xx\">省市区</view>\r\n\t\t\t\t<picker mode=\"multiSelector\" :range=\"ssqRange\" range-key=\"label\" :value=\"ssqIndex\" class=\"pick\" @columnchange=\"ssqColumnChange\" @change=\"ssqChange\">\r\n\t\t\t\t\t<view :style=\"ssqValue?'':'color:#999'\">{{ssqValue?ssqValue:'请选择'}}</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"textarea\">\r\n\t\t\t\t<view class=\"xx\">详细地址</view>\r\n\t\t\t\t<textarea auto-height=\"true\" maxlength=\"-1\" :placeholder=\"address?address:'请输入详细地址'\" v-model=\"address\"></textarea>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"swich\">\r\n\t\t\t<view>设为默认地址</view>\r\n\t\t\t<switch :checked=\"is_default == 1?true:false\" color=\"#3478FB\" style=\"transform:scale(0.7)\" @change=\"switchChange\"/>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @click=\"getSubmit\">保存</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttype: '',// 1添加 2编辑\r\n\t\t\t\tid: '',\r\n\t\t\t\t\r\n\t\t\t\tconsignee: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\t\r\n\t\t\t\taddress: '',\r\n\t\t\t\tis_default: '',\r\n\t\t\t\t\r\n\t\t\t\tssqData: [],// 省市区的级联数据\r\n\t\t\t\tssqRange: [],// 封装好的省市区下拉\r\n\t\t\t\tssqIndex: [],// 省市区选中项index\r\n\t\t\t\tssqValue: '',// 目前选中值的文字\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.getArea()\r\n\t\t\tthis.type = option.type\r\n\t\t\tif(option.type == 1){\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '添加'\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tthis.id = option.id\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '编辑'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 省市区数据\r\n\t\t\tgetArea(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.getArea(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.ssqData = data\n\t\t\t\t\t\tif(that.ssqData.length > 0){\n\t\t\t\t\t\t\tthat.ssqRange = [\n\t\t\t\t\t\t\t\tthat.ssqData,\n\t\t\t\t\t\t\t\tthat.ssqData[0].list,\n\t\t\t\t\t\t\t\tthat.ssqData[0].list[0].list\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t\tthat.ssqIndex = [0,0,0];// 索引默认第一个\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(that.id){\r\n\t\t\t\t\t\t\tthat.getData()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 多列里的每一列选项改变\r\n\t\t\tssqColumnChange(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet {column,value} = e.detail;\r\n\t\t\t\tthat.ssqIndex[column] = value;\r\n\t\t\t\t//当前操作的这一列赋新值\r\n\t\t\t\tif(column == 0){\r\n\t\t\t\t\t//第一列改变\r\n\t\t\t\t\tthat.ssqIndex[1] = 0;\r\n\t\t\t\t\tthat.ssqIndex[2] = 0;\r\n\t\t\t\t}else if(column == 1){\r\n\t\t\t\t\t//第二列改变\r\n\t\t\t\t\tthat.ssqIndex[2] = 0;\r\n\t\t\t\t}\r\n\t\t\t\tthat.ssqRange = [\r\n\t\t\t\t\tthat.ssqData,\r\n\t\t\t\t\tthat.ssqData[that.ssqIndex[0]].list,\r\n\t\t\t\t\tthat.ssqData[that.ssqIndex[0]].list[that.ssqIndex[1]].list\r\n\t\t\t\t];\r\n\t\t\t},\r\n\t\t\t// 点击确定按钮，选项改变\r\n\t\t\tssqChange(e){\r\n\t\t\t\tthis.ssqIndex = e.detail.value;\r\n\t\t\t\t// 计算选中项的中文\r\n\t\t\t\tthis.getSsqValue();\r\n\t\t\t},\r\n\t\t\tgetSsqValue(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet result = \"\"\r\n\t\t\t\tif(that.ssqRange.length > 0){\r\n\t\t\t\t\tthat.ssqRange.forEach((e,i)=>{\r\n\t\t\t\t\t\tresult += that.ssqRange[i][that.ssqIndex[i]].label + \"-\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthat.ssqValue = result.slice(0,-1);\r\n\t\t\t},\r\n\t\t\t// 获取地址详情\r\n\t\t\tgetData(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tid: that.id\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.infoAddress(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code === 1) {\r\n\t\t\t\t\t\tlet province_index = ''\r\n\t\t\t\t\t\tlet city_index = ''\r\n\t\t\t\t\t\tlet area_index = ''\r\n\t\t\t\t\t\tthat.consignee = data.consignee\r\n\t\t\t\t\t\tthat.phone = data.phone\r\n\t\t\t\t\t\tthat.address = data.address\r\n\t\t\t\t\t\tthat.is_default = data.is_default\r\n\t\t\t\t\t\tthat.ssqValue = data.province_name + \"-\" + data.city_name + \"-\" + data.area_name\r\n\t\t\t\t\t\tthat.ssqData.forEach((p,ip)=>{\r\n\t\t\t\t\t\t\t// 省\r\n\t\t\t\t\t\t\tif(p.value == data.province_id){\r\n\t\t\t\t\t\t\t\tprovince_index = ip\r\n\t\t\t\t\t\t\t\tthat.ssqRange[1] = p.list\r\n\t\t\t\t\t\t\t\tp.list.forEach((c,ic)=>{\r\n\t\t\t\t\t\t\t\t\t// 市\r\n\t\t\t\t\t\t\t\t\tif(c.value == data.city_id){\r\n\t\t\t\t\t\t\t\t\t\tcity_index = ic\r\n\t\t\t\t\t\t\t\t\t\tthat.ssqRange[2] = c.list\r\n\t\t\t\t\t\t\t\t\t\tc.list.forEach((e,i)=>{\r\n\t\t\t\t\t\t\t\t\t\t\t// 区\r\n\t\t\t\t\t\t\t\t\t\t\tif(e.value == data.area_id){\r\n\t\t\t\t\t\t\t\t\t\t\t\tarea_index = i\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthat.ssqIndex = [province_index,city_index,area_index]\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 设置默认\r\n\t\t\tswitchChange(e){\r\n\t\t\t\tconsole.log(e.detail.value)\r\n\t\t\t\tif(e.detail.value){\r\n\t\t\t\t\tthis.is_default = 1\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.is_default = 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tgetSubmit(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tid: that.id?that.id:'0',\r\n\t\t\t\t\tis_default: that.is_default,\r\n\t\t\t\t\tconsignee: that.consignee,\r\n\t\t\t\t\tarea_text: that.ssqValue,\r\n\t\t\t\t\taddress: that.address,\r\n\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.editAddress(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code === 1) {\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content{\r\n\t\tpadding: 20rpx 24rpx;\r\n\t}\r\n\t.form{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.form .nr{\r\n\t\tborder-bottom: 1rpx solid rgba(201,201,201,0.5);\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.form .nr .xx{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.form .nr input{\r\n\t\twidth: 80%;\r\n\t\theight: 100rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #000;\r\n\t\ttext-align: right;\r\n\t}\r\n\t.form .nr .pick view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #000;\r\n\t}\r\n\t.form .textarea{\r\n\t\tpadding: 20rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.form .textarea .xx{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.form .textarea textarea{\r\n\t\twidth: 80%;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t\ttext-align: right;\r\n\t}\r\n\t.swich{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tmargin-bottom: 200rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.swich view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 108rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.btn{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 44rpx;\r\n\t}\r\n\t.list .nr{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 24rpx 22rpx;\r\n\t}\r\n\t.list .nr .name{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\t.list .nr .address{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editor_address.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editor_address.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974065\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}