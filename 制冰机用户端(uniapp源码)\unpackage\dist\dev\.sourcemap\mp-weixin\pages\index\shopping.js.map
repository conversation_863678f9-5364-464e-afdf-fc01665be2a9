{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?a132", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?5394", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?bfe1", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?fe42", "uni-app:///pages/index/shopping.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?7c0b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/shopping.vue?cba6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "navHeight", "statusBarHeight", "headerHeight", "backgroud", "api", "type", "search", "scrollTitle", "list", "page", "limit", "loading", "onPageScroll", "onLoad", "onReachBottom", "onPullDownRefresh", "uni", "title", "methods", "getStroll", "that", "icon", "duration", "catch", "getType", "getList", "name", "stroll_category_id", "res", "code", "msg", "setTimeout", "goCar", "url", "goDetails", "getSearch", "getAddcar", "goods_list"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAwtB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiD5uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MAAA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACA;EACAC;IACA;MACA;IACA;IACA;IACA;IACA;EACA;EACA;EACAC;IACAC;MACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA;UACAA;QACA;UACAJ;YACAK;YACAJ;YACAK;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACAC;QACAlB;QACAC;MACA;MACAU;QACA,gBAIAQ;UAHAC;UACA9B;UACA+B;QAEAC;UACAf;QACA;QACA;UACA;YACAI;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACAJ;YACAK;YACAJ;YACAK;UACA;QACA;MACA,GACAC;QACAQ;UACAf;QACA;MACA;MACAe;QACAf;MACA;IACA;IAEA;IACAgB;MACA;QACAhB;UACAiB;QACA;MACA;QACAjB;UACAiB;QACA;MACA;IACA;IACA;IACAC;MACAlB;QACAiB;MACA;IACA;IACA;IACAE;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACApB;UACAiB;QACA;MACA;QACA;UACAI,aACA;YACA;YACA;UACA;QAEA;QACA;UACA,iBAIAT;YAHAC;YACA9B;YACA+B;UAEA;YACAd;cACAK;cACAJ;cACAK;YACA;UACA;YACAN;cACAK;cACAJ;cACAK;YACA;UACA;QACA,GACAC,sBAEA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AClPA;AAAA;AAAA;AAAA;AAAyhC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;;ACA7iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/shopping.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/shopping.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shopping.vue?vue&type=template&id=54c66c3e&\"\nvar renderjs\nimport script from \"./shopping.vue?vue&type=script&lang=js&\"\nexport * from \"./shopping.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shopping.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/shopping.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopping.vue?vue&type=template&id=54c66c3e&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopping.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopping.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"heard\" :style=\"{paddingTop: statusBarHeight + 'px'}\">\r\n\t\t\t<view class=\"title\" :style=\"{height: navHeight + 'px'}\">逛商城</view>\r\n\t\t\t<view class=\"search\">\r\n\t\t\t\t<input type=\"text\" placeholder=\"请输入搜索内容\" confirm-type='搜索' placeholder-style=\"color:#fff\" v-model=\"search\" @confirm='getSearch' />\r\n\t\t\t</view>\r\n\t\t\t<scroll-view scroll-x=\"true\" class=\"scroll\">\r\n\t\t\t\t<view class=\"scroll-item\" :class=\"type == '0'?'on':''\" @click=\"getType('0')\">全部</view>\r\n\t\t\t\t<view class=\"scroll-item\" v-for=\"(item,index) in scrollTitle\" :class=\"type == item.id?'on':''\" @click=\"getType(item.id)\">{{item.name}}</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"list\" :style=\"{paddingTop: 'calc(' + headerHeight + 'px' + ' + 200rpx)'}\" v-if=\"list.length > 0\">\r\n\t\t\t<view class=\"nr\" @click=\"goDetails(item.id)\" v-for=\"(item,index) in list\">\r\n\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t<image :src=\"api + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<view class=\"numb\">\r\n\t\t\t\t\t\t\t<view>￥{{item.price}}</view>\r\n\t\t\t\t\t\t\t<view>￥{{item.market_price}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"car\" @click.stop=\"getAddcar(item.id)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon_carb.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<uni-load-more :status=\"loading\" v-if=\"list.length > 0\"></uni-load-more>\r\n\t\t\r\n\t\t<view class=\"zw\" :style=\"{paddingTop: '80%'}\" v-else>\r\n\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view>暂无商品</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shopcar\" @click=\"goCar\">\r\n\t\t\t<image src=\"/static/icon_car.png\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { mapState, mapMutations} from 'vuex'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavHeight: this.$cache.fetchCache('navHeight'),\r\n\t\t\t\tstatusBarHeight: this.$cache.fetchCache('statusBarHeight'),\r\n\t\t\t\theaderHeight: this.$cache.fetchCache('headerHeight'),\r\n\t\t\t\tbackgroud: 'transparent',\r\n\t\t\t\t\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\ttype: '0',// 分类\r\n\t\t\t\tsearch: '',// 搜索内容\r\n\t\t\t\t\r\n\t\t\t\tscrollTitle:[],// 分类列表\r\n\t\t\t\t\r\n\t\t\t\tlist:[],// 商品列表\r\n\t\t\t\t\r\n\t\t\t\tpage: 1, // 分页\r\n\t\t\t\tlimit: 10,// 每页条数\r\n\t\t\t\tloading: \"noMore\", // noMore-无更多数据 loading-加载中 more-上拉加载更多\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\tif (e.scrollTop) {\r\n\t\t\t\tthis.backgroud = '#fff'\r\n\t\t\t} else if (e.scrollTop || e.scrollTop < 5) { // 这里<5 防止监听0 失败\r\n\t\t\t\tthis.backgroud = 'transparent'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.getStroll()\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\t// 上拉加载\r\n\t\tonReachBottom() {\r\n\t\t\tif(this.loading == 'noMore'){\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.loading = 'loading';\r\n\t\t\tthis.page++;\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\t// 下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tthis.loading = 'loading';\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取分类列表\r\n\t\t\tgetStroll(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.strollList(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.scrollTitle = res.data.data\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 分类切换\r\n\t\t\tgetType(e){\r\n\t\t\t\tthis.type = e\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 商品列表\r\n\t\t\tgetList(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tname: that.search,\r\n\t\t\t\t\tstroll_category_id: that.type,\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.strollGoods(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tif (that.page == 1) {\r\n\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (var i = 0; i < data.list.length; i++) {\r\n\t\t\t\t\t\t\tthat.list.push(data.list[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.list.length <= that.limit) {\r\n\t\t\t\t\t\t\tthat.loading = 'noMore';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.stopPullDownRefresh(); //得到数据后停止下拉刷新\r\n\t\t\t\t}, 300);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 购物车\r\n\t\t\tgoCar(){\r\n\t\t\t\tif(!this.$cache.fetchCache('token')){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesCar/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 商品详情\r\n\t\t\tgoDetails(e){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/pagesHome/goods_details?id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//  搜索\r\n\t\t\tgetSearch(event){\r\n\t\t\t\tthis.search = event.target.value;\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 加入购物车\r\n\t\t\tgetAddcar(e){\r\n\t\t\t\tif(!this.$cache.fetchCache('token')){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tgoods_list: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"goods_id\": e,\r\n\t\t\t\t\t\t\t\t\"goods_num\": 1,\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$api.Addcar(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: '加入购物车成功',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: '加入购物车失败',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.heard{\r\n\t\tbackground: #5A9AF1;\r\n\t}\r\n\t.search{\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 24rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\t.search input{\r\n\t\twidth: 100%;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 0 20rpx 0 70rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.4) url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAdCAYAAABWk2cPAAAAAXNSR0IArs4c6QAAAwBJREFUSEu1lkuIjmEUx39/FEKpIUnIKLcFuTdZuOVWWFiMaxIWFiyU3CcWJsqOlUKuk8soykaEDTFRmISFey5FhBDh6EzPq2de7/tdjDm77zvnPL/3nOc85xxRQMysApgPTAGGAX2D+SvgHnANqAduSbJCZ8U6ZRmaWQ9gE7CqxIMuA5slXSnF/i+omU0F6gCPslzZCtRK+lHIsRnUzOYAp1IOjcB54DDwBvDUDgDGAfOASUCbyOc0sEDS1zzwH2iI8Fxk+AFYJ2lPkXsfCBwBRkV2u4DVkn5l+TZBwx3ejVL6Ehgp6XWp+TWzox5hZD9L0tlC0N3AymDgEQ4qB5gcbGZXgarw+71Xu6RPabDCs3gbKVYUS2mRdH8EugSbakkns6AeoUfq0ihpaKkpzXluO7wWgq5B0tgs6BlgdlB4uW9uIbQT4NEmFd1dUpxJPL1PgT4BNFzSrZZAQ2E+ibrXaEk34jMdGrevCknv/gP0NpBc02RJFwtB2+a9rXI+xMyKQv1N9gyHDpZ0vxxATjE9BCqDbpKkS+lIPfSJ4c/lkvb9B6i/zc7hnEpJj9PQWmBj+POCJB9j/yxmNhc4Fg744vD02PNCGgHcDEbeK4dIevCvVDN7BPQL/msl7cx6p95/PcUTgvKOJB/YZYuZbQtzOPHtL8k/opkkDX800BBp6iQtLIdqZtMBb/Btg982STVZZ8SjzQfwlsgos4XlVKtHuD4CPgvX9LkYtF0Y1D6YE/EqPAFskOQDvJmYWTXgd5Z0NNf78B4o6XleptKbQ3tguw/glIMXmH+9jz2XroDvUR1Sdm5TJemlmXUEZvrylv6ArB3JG/W0sCf54aWKp9jv8VsA+gvoDXwHlkny7aJJMrdBV5iZP+4ZwBpgTA7ZU+7prY8bQEj78cjnJ7BU0qGC0BhiZt2AXtE641//It1pEh8z6w749PIUJ+Jgj/hgbqSl5jTPzswWAQeiinZTB49vNWi4osXA/hS4plWhAbwE2BuBWzfS6I59MfdJdl7S9d8JRRsZHsT/dgAAAABJRU5ErkJggg==) no-repeat left 20rpx center;\r\n\t\tbackground-size: 28rpx auto;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.scroll {\r\n\t\tbackground-color: #fff;\r\n\t\twhite-space: nowrap;/*必须要有，规定段落中的文本不进行换行*/\r\n\t\tpadding: 0 24rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.scroll-item {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: inline-block;/*必须要有*/\r\n\t\tmargin-right: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.scroll-item:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\t\r\n\t.scroll .scroll-item.on{\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.list{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t.list .nr{\r\n\t\twidth: 32%;\r\n\t\tborder-radius: 10rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 2%;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.list .nr:nth-child(3n){\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.list .nr:nth-child(-n + 3){\r\n\t\tmargin-top: 0;\r\n\t}\r\n\t.list .nr .pic{\r\n\t\twidth: 100%;\r\n\t\theight: 310rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.list .nr .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\t.list .nr .xx{\r\n\t\tpadding: 12rpx 24rpx 15rpx;\r\n\t}\r\n\t.list .nr .xx .name{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.list .nr .xx .price{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.list .nr .xx .price .numb view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.list .nr .xx .price .numb view:last-child{\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: normal;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\t.list .nr .price .car{\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t}\r\n\t.list .nr .price .car image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t.shopcar{\r\n\t\tposition: fixed;\r\n\t\twidth: 68rpx;\r\n\t\theight: 68rpx;\r\n\t\tright: 24rpx;\r\n\t\tbottom: 20%;\r\n\t}\r\n\t.shopcar image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopping.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopping.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974019\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}