<?php

namespace app\api\controller\shop;

use app\admin\model\Area;
use app\admin\model\UserAddress;
use app\common\controller\Api;
use app\common\service\GeocodeService;
//未用到
class Address extends Api
{
    //获取用户购买过的售货机地址列表
    public function list(){

    }

    public function index()
    {
        $user = $this->auth->getUser();
        $this->success('收货地址', UserAddress::all([
            'user_id' => $user->id,
            'deletetime' => null
        ]));
    }

    public function defaults()
    {
        $user = $this->auth->getUser();
        $this->success('默认收货地址', UserAddress::get([
            'user_id' => $user->id,
//            'deletetime' => null,
            'is_default' => '1'
        ]));
    }

    public function area()
    {
        $data = Area::where('level', 1)->order('id asc')->field('id as value, name as label, pid, level')->select();

        foreach ($data as $k => $p) {
            $list= Area::where(['level' => 2, 'pid' => $p->value])->order('id asc')->field('id as value, name as label, pid, level')->select();
//            $list=[];
            foreach ($list as $i => $c) {
              $list[$i]['list'] = Area::where(['level' => 3, 'pid' => $c->value])->order('id asc')->field('id as value, name as label, pid, level')->select();
//                $list[$i]['list']=1;
            }
            $data[$k]['list'] = $list;

        }

        $this->success('省市区', $data);

    }

    public function edit()
    {
        $params = $this->request->post();
        $user = $this->auth->getUser();
        $areaNameArray = explode("-", $params['area_text']);
        $province = Area::get(['name' => $areaNameArray[0], 'level' => 1]);
        $city = Area::get(['name' => $areaNameArray[1], 'level' => 2]);
        $area = Area::get(['name' => $areaNameArray[2], 'pid' => $city->id, 'level' => 3]);
        if(!$province || !$city || !$area) {
            $this->error('城市信息错误！');
        }

        $consignee = $params['consignee'] ?? '';
        $phone = $params['phone'] ?? '';
        $is_default = $params['is_default'] ?? '';
        $address = $params['address'] ?? '';
        $id = $params['id'] ?? 0;
        $pattern = '/^1[3-9]\d{9}$/';
        if(preg_match($pattern, $phone) !== 1){
            $this->error('手机号有误');
        }

        // 使用地理编码服务获取经纬度
        $latitude = null;
        $longitude = null;

        // 构建完整地址
        $fullAddress = $province->name . $city->name . $area->name . $address;

        // 调用腾讯地图API获取经纬度
        $location = GeocodeService::getLocationByAddress($fullAddress, $city->name);
        if ($location) {
            $latitude = $location['latitude'];
            $longitude = $location['longitude'];
        }
        if($is_default){
            UserAddress::where([
                'user_id'=>$user->id
            ])->update([
                'is_default' => 0,
            ]);
        }else{
            $is_default=0;
        }


        if($id>0){
            $edit = UserAddress::update([
                'consignee' => $consignee,
                'phone' => $phone,
                'province_id' => $province->id,
                'province_name' => $province->name,
                'city_id' => $city->id,
                'city_name' => $city->name,
                'area_id' => $area->id,
                'area_name' => $area->name,
                'is_default' => $is_default,
                'latitude' => (isset($latitude) && $latitude) ? $latitude : null,
                'longitude' => (isset($longitude) && $longitude) ? $longitude : null,
                'user_id' => $user->id,
                'address' => $address,
                'id' => $id
            ]);
        }else{
            $edit = UserAddress::create([
                'consignee' => $consignee,
                'phone' => $phone,
                'province_id' => $province->id,
                'province_name' => $province->name,
                'city_id' => $city->id,
                'city_name' => $city->name,
                'area_id' => $area->id,
                'area_name' => $area->name,
                'is_default' => $is_default,
                'latitude' => (isset($latitude) && $latitude) ? $latitude : null,
                'longitude' => (isset($longitude) && $longitude) ? $longitude : null,
                'user_id' => $user->id,
                'address' => $address
            ]);
        }
        $this->success('地址信息已保存', $edit);
    }

    public function info()
    {
        $params = $this->request->get();
        $id = $params['id'] ?? 0;
        $user = $this->auth->getUser();
        $this->success('地址详情', UserAddress::get(['id' => $id, 'user_id' => $user->id]));
    }

    public function del()
    {
        $params = $this->request->post();
        $id = $params['id'] ?? 0;
        $user = $this->auth->getUser();
        $this->success('删除成功', UserAddress::where(['id' => $id, 'user_id' => $user->id])->delete());

    }
}