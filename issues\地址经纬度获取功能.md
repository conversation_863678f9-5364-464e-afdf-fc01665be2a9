# 地址经纬度获取功能实现

## 任务背景
用户需要在添加/编辑收货地址时自动获取经纬度信息，用于后续的距离计算功能。

## 数据库支持
- 表 `fa_user_address` 已包含 `latitude` 和 `longitude` 字段
- 字段类型：`decimal(10,6)` 支持精确的经纬度存储

## 实施方案
采用腾讯地图API地理编码服务，自动将地址转换为经纬度。

### API配置
- 服务商：腾讯地图
- API密钥：ESUBZ-3EMW7-TAFXF-PKE3L-BKWPO-BSFCJ
- 接口地址：https://apis.map.qq.com/ws/geocoder/v1/

## 代码修改

### 1. 创建地理编码服务类
**文件：** `application/common/service/GeocodeService.php`
**功能：**
- 封装腾讯地图API调用逻辑
- 提供地址转经纬度的方法
- 包含错误处理和日志记录
- 支持批量地理编码（预留）

### 2. 修改地址编辑接口
**文件：** `application/api/controller/shop/Address.php`
**修改内容：**
- 添加 GeocodeService 引用
- 在 edit() 方法中集成地理编码功能
- 构建完整地址并调用API获取经纬度
- 将获取的经纬度保存到数据库

### 3. 实现逻辑
1. 用户提交地址信息（省市区 + 详细地址）
2. 后端构建完整地址字符串
3. 调用腾讯地图API进行地理编码
4. 获取返回的经纬度信息
5. 将经纬度与其他地址信息一起保存到数据库

## 技术特点
- **自动化：** 用户无需额外操作，保存地址时自动获取经纬度
- **容错性：** API调用失败时不影响地址保存功能
- **日志记录：** 记录API调用错误，便于问题排查
- **性能优化：** 设置10秒超时，避免长时间等待

## 使用效果
- 新增地址时：自动获取经纬度并保存
- 编辑地址时：重新获取经纬度并更新
- 数据库中的 latitude 和 longitude 字段将被正确填充
- 为后续的距离计算功能提供数据支持

## 注意事项
1. 需要确保服务器能够访问腾讯地图API
2. API调用有频率限制，需要注意使用量
3. 地址信息越详细，地理编码精确度越高
4. 建议定期检查API密钥的有效性和配额使用情况

---

# 订单确认页面问题修复

## 问题描述
1. **machine_id参数错误**：传递的是机器编号(machine_no)而不是机器ID
2. **total_weight重量错误**：自定义重量100变成了1000

## 修复方案

### 1. 添加获取机器信息API
**文件：** `application/api/controller/machine/Index.php`
- 新增 `info()` 方法
- 根据machine_no获取机器的完整信息（包含id）

### 2. 修改前端API调用
**文件：** `制冰机用户端(uniapp源码)/http/api.js`
- 添加 `getMachineByNo()` 方法
- 调用 `/api/machine/info` 接口

### 3. 修复vending页面数据传递
**文件：** `制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue`
- 区分商品数量和重量字段
- 修正自定义重量的处理逻辑
- 确保传递正确的重量值

### 4. 修复order-confirm页面
**文件：** `制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue`
- 添加 `machineInfo` 数据字段
- 新增 `getMachineInfo()` 方法
- 修改 `calculateDeliveryFee()` 方法使用正确的machine_id和重量

## 修复效果
- ✅ machine_id参数正确：使用机器的数据库ID而不是编号
- ✅ total_weight重量正确：自定义重量100不再变成1000
- ✅ 配送费计算正常：基于正确的参数进行计算

## 优化方案（第二版）

### 问题分析
用户指出vending页面已经通过 `/api/machine/index` 接口获取到了机器的完整信息（包含id），没必要再额外请求接口。

### 优化修改

**1. vending页面优化**
- 添加 `machine_info` 字段保存机器完整信息
- 在 `getInfo()` 方法中保存 `data.machine` 到 `machine_info`
- 在传递给order-confirm的数据中直接包含 `machine_id`

**2. order-confirm页面简化**
- 删除 `getMachineInfo()` 方法和相关API调用
- 删除 `machineInfo` 数据字段
- 简化 `calculateDeliveryFee()` 方法，直接使用传递过来的 `machine_id`

**3. 删除不必要的API接口**
- 不再需要 `/api/machine/info` 接口
- 不再需要 `getMachineByNo()` API调用

### 优化效果
- 🚀 减少了一次不必要的API请求
- 🚀 简化了代码逻辑，提高了性能
- 🚀 利用了已有的数据，避免重复获取
- ✅ 保持了功能的正确性
