<?php

namespace app\api\controller\shop;

use app\common\controller\Api;
use app\common\model\shop\Order;
use app\common\model\shop\OrderGoods;
use app\common\model\User;
use app\common\service\shop\PayService;
use app\common\service\UserCouponsService;
use app\common\service\UserService;
use Symfony\Component\HttpFoundation\Request;
use Yansongda\Pay\Gateways\Wechat\Support;

//商城支付
class Pay extends Api
{

    protected $noNeedLogin = ['notify'];

    //收银台
    public function index(PayService $payService)
    {
        [$order_sn] = $this->params([
            ['order_sn', ''],
        ]);
        if (empty($order_sn)) $this->error('订单编号为空');

        $this->success('', $payService->center($order_sn));
    }

    /**
     * 拉起支付
     */
    public function create()
    {
        [$order_sn, $payment, $openid] = $this->params([
            ['order_sn', 0],
            ['payment', ''], //支付方式 wechat,alipay,wallet,score,xinyi
            ['openid', ''],  //微信支付时可传,不传的话服务器获取
        ]);
        if (empty($order_sn)) $this->error('订单编号为空');
        if (!$payment || !in_array($payment, ['wechat', 'alipay', 'wallet', 'score'])) {
            $this->error("支付类型有误");
        }
        $payService = new PayService($this->platform, $payment, $openid);
        $this->success('success', $payService->create($order_sn));
    }

    /**
     * 支付成功回调
     */
    public function notify()
    {
        $content = $content ?? Request::createFromGlobals()->getContent();
        $res_data = Support::fromXml($content);
        $out_trade_no = $res_data['out_trade_no'];

        if (strpos($out_trade_no, 'TO') === 0) {
            $order = (new \app\common\model\shop\TradeOrder())->where('order_sn', $out_trade_no)->find();
            if($order['status']==0){
                $user = User::where('id', $order->user_id)->find();
                (new \app\common\model\shop\TradeOrder())->where('order_sn', $out_trade_no)->update([
                    'status'=>1
                ]);
                // 用户充值，给用户增加余额
                UserService::changeMoney($user, $order['real_amount'], 'recharge', '充值金额', $order['id'], [
                    'order_id' => $order->id,
                    'order_sn' => $order->order_sn,
                ]);
            }

        }else{
            $order = (new Order())->where('order_sn', $out_trade_no)->find();
            if (!$order || $order->status > 0) {
                // 订单不存在，或者订单已支付
                $this->error("订单不存在，或者订单已出货");
            }

            $order->status = 1;
            $order->paytime = time();
            $order->save();
            //充值订单，

            OrderGoods::where('order_id', $order['id'])->update(['paytime' => $order['paytime'], 'pay_type' => $order['pay_type'], 'pay_status' => 1]);
            $list = OrderGoods::where('order_id', $order['id'])->select();
            foreach ($list as $k=>$v){
                $goods = \app\admin\model\vending\Goods::where([
                    'id'=>$v['goods_id']
                ])->find();
                \app\admin\model\vending\Goods::where([
                    'id'=>$v['goods_id']
                ])->update([
                    'stock'=>$goods['stock'] - $v['goods_num'],
                    'shop_sales'=>$goods['shop_sales']+$v['goods_num'],
                    'sales'=>$goods['sales']+$v['goods_num']
                ]);
            }
        }


//        \think\Queue::push('\app\common\job\shop\Order@payed', ['order_id' => $order['id'], 'user_id' => $order['user_id']]);

        $this->success('操作成功，请等待出货！');
    }
}
