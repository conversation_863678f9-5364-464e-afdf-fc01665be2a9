{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?bcb7", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?a42c", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?619b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?bc2f", "uni-app:///pages/pagesMy/shop_order.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?5e02", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/shop_order.vue?b574"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "status", "list", "page", "limit", "loading", "kefu_tel", "onLoad", "methods", "getUser", "that", "uni", "icon", "title", "duration", "catch", "getStatus", "getList", "res", "code", "msg", "time", "onReachBottom", "onPullDownRefresh", "getCancel", "content", "success", "order_id", "console", "getComplete", "id", "goDetail", "url", "goPay", "phone", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2D9uB;EACAC;IACA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA;UACAA;QACA;UACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAhB;QACAE;QACAC;MACA;MACAM;QACA,gBAKAQ;UAJAC;UACApB;UACAqB;UACAC;QAEA;UACA;YACAX;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAC;QACA;UACAA;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IAEA;IACAO;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAb;QACAE;QACAY;QACAC;UACA;YACA;cACAC;YACA;YACAjB;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAJ;gBACAA;gBACAA;gBACAA;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;YACA,GACAC,sBAEA;UACA;YACAa;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAlB;QACAE;QACAY;QACAC;UACA;YACA;cACAI;YACA;YACApB;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAJ;gBACAA;gBACAA;gBACAA;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;YACA,GACAC,sBAEA;UACA;YACAa;UACA;QACA;MACA;IACA;IACA;IACAG;MACApB;QACAqB;MACA;IACA;IACA;IACAC;MACA;QACAtB;UACAqB;QACA;MACA;QACArB;UACAqB;QACA;MACA;IACA;IAEA;IACAE;MACAN;MACAjB;QACAwB;MACA,uBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzQA;AAAA;AAAA;AAAA;AAA2hC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACA/iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/shop_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/shop_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shop_order.vue?vue&type=template&id=516689dd&\"\nvar renderjs\nimport script from \"./shop_order.vue?vue&type=script&lang=js&\"\nexport * from \"./shop_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shop_order.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/shop_order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop_order.vue?vue&type=template&id=516689dd&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop_order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"order\">\r\n\t\t\t<scroll-view scroll-x=\"true\" class=\"nav\">\r\n\t\t\t\t<view :class=\"status==''?'active':''\" @click=\"getStatus('')\">全部</view>\r\n\t\t\t\t<view :class=\"status=='0'?'active':''\" @click=\"getStatus('0')\">待付款</view>\r\n\t\t\t\t<view :class=\"status=='1'?'active':''\" @click=\"getStatus('1')\">待发货</view>\r\n\t\t\t\t<view :class=\"status=='2'?'active':''\" @click=\"getStatus('2')\">待收货</view>\r\n\t\t\t\t<view :class=\"status=='3'?'active':''\" @click=\"getStatus('3')\">已完成</view>\r\n\t\t\t\t<view :class=\"status=='-1'?'active':''\" @click=\"getStatus('-1')\">已取消</view>\r\n\t\t\t\t<view :class=\"status=='-2'?'active':''\" @click=\"getStatus('-2')\">交易关闭</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class=\"list\" v-if=\"list.length > 0\">\r\n\t\t\t\t<view class=\"nr\" @click=\"goDetail(items.id)\" v-for=\"(items,index) in list\">\r\n\t\t\t\t\t<view class=\"numb\">\r\n\t\t\t\t\t\t<view>订单编号：{{items.order_sn}}</view>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '0'\">待付款</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 1\">待发货</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 2\">待收货</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == 3\">已完成</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '-1'\">已取消</text>\r\n\t\t\t\t\t\t<text v-if=\"items.status == '-2'\">交易关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t<view class=\"prodxx\" v-for=\"(item,index) in items.goods\">\r\n\t\t\t\t\t\t\t<image :src=\"api + item.goods_image\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.goods_name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"num\">数量：{{item.goods_num}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" v-if=\"items.type == 'score'\">{{item.goods_price}}积分</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" v-else>￥{{item.goods_price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t<view v-if=\"items.type != 'score'\">优惠：￥{{items.discount_amount || '0.00'}}，实付款：</view>\r\n\t\t\t\t\t\t<view v-else>实付：</view>\r\n\t\t\t\t\t\t<text v-if=\"items.type == 'score'\">{{items.score_amount || '0.00'}}积分</text>\r\n\t\t\t\t\t\t<text v-else>￥{{items.pay_fee || '0.00'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t\t<view v-if=\"items.status == 0\" @click.stop=\"getCancel(items.id)\">取消订单</view>\r\n\t\t\t\t\t\t<view class=\"active\" v-if=\"items.status == 0\" @click.stop=\"goPay(items.order_sn,items.type)\">立即支付</view>\r\n\t\t\t\t\t\t<view class=\"active\" v-if=\"items.status == 2\" @click.stop=\"getComplete(items.id)\">确认收货</view>\r\n\t\t\t\t\t\t<view @click.stop=\"phone(kefu_tel)\">联系客服</view><!-- 拨打电话 -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-load-more :status=\"loading\"></uni-load-more>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"zw\" v-else>\r\n\t\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view>暂无订单</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\tstatus: '',// 订单状态:-2=交易关闭,-1=已取消,0=未支付,1=已支付,2=已发货,3=已完成\r\n\t\t\t\tlist: [],// 订单列表\r\n\t\t\t\tpage: 1, // 分页\r\n\t\t\t\tlimit: 10,// 每页条数\r\n\t\t\t\tloading: \"noMore\", // noMore-无更多数据 loading-加载中 more-上拉加载更多\r\n\t\t\t\t\r\n\t\t\t\tkefu_tel: '',// 客服电话\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.getList()\r\n\t\t\tthis.getUser()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 个人资料\r\n\t\t\tgetUser(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.userInfo(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.kefu_tel = res.data.data.kefu_tel\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 状态切换\r\n\t\t\tgetStatus(e){\r\n\t\t\t\tthis.status = e\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 订单列表\r\n\t\t\tgetList(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tstatus: that.status,\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.OrderList(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tif (that.page == 1) {\r\n\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (var i = 0; i < data.list.length; i++) {\r\n\t\t\t\t\t\t\tthat.list.push(data.list[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.list.length <= that.limit) {\r\n\t\t\t\t\t\t\tthat.loading = 'noMore';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.stopPullDownRefresh(); // 停止下拉刷新动画\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上拉加载\r\n\t\t\tonReachBottom() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 下拉刷新\r\n\t\t\tonPullDownRefresh() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 取消订单\r\n\t\t\tgetCancel(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确定取消订单?',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\torder_id: e,\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$api.cancel(params).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: '订单取消成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthat.loading = 'loading';\r\n\t\t\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\t\t\t\tthat.getList();\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 确认收货\r\n\t\t\tgetComplete(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认收货?',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\tid: e,\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$api.complete(params).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthat.loading = 'loading';\r\n\t\t\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\t\t\t\tthat.getList();\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 订单详情\r\n\t\t\tgoDetail(e){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: 'order_details?type=2' + '&id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去支付\r\n\t\t\tgoPay(e,t){\r\n\t\t\t\tif(t == 'store'){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesPay/index?type=3' + '&order_sn=' + e\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesPay/index?type=2' + '&order_sn=' + e\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 联系客服\r\n\t\t\tphone(item) {\r\n\t\t\t\tconsole.log('打电话',item)\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: item.toString()\r\n\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.order{\r\n\t\tpadding: 110rpx 24rpx 20rpx;\r\n\t}\r\n\t.order .nav{\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twhite-space: nowrap;/*必须要有，规定段落中的文本不进行换行*/\r\n\t}\r\n\t.order .nav view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-block;/*必须要有*/\r\n\t\tmargin: 0 24rpx;\r\n\t}\r\n\t.order .nav view.active{\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .nav view.active::after{\r\n\t\tcontent: '';\r\n\t\twidth: 100%;\r\n\t\theight: 4rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #5A9AF1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\t.order .list .nr{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .list .nr .numb{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t\tpadding: 0 22rpx;\r\n\t}\r\n\t.order .list .nr .numb view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .list .nr .numb text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #5A9AF1;\r\n\t}\r\n\t.order .list .nr .product{\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\t.order .list .nr .product .prodxx{\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.order .list .nr .product .prodxx:last-child{\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t.order .list .nr .product .prodxx image{\r\n\t\twidth: 184rpx;\r\n\t\theight: auto;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\t.order .list .nr .product .prodxx .xx{\r\n\t\twidth: 440rpx;\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\t.order .list .nr .product .prodxx .xx .name{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 38rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .list .nr .product .prodxx .xx .num{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #C5C5C5;\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\t.order .list .nr .product .prodxx .xx .price{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .list .nr .money{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t\tborder-top: 1px solid #E4E4E4;\r\n\t\tpadding: 0 24rpx;\r\n\t}\r\n\t.order .list .nr .money view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .list .nr .money text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.order .list .nr .btn{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t\tpadding: 0 24rpx 20rpx;\r\n\t}\r\n\t.order .list .nr .btn view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 62rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 20rpx;\r\n\t\tbackground: #F6F6F6;\r\n\t\tborder-radius: 70rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t.order .list .nr .btn view.active{\r\n\t\tbackground: #3478FB;\r\n\t\tcolor: #fff;\r\n\t}\r\n\t.order .list .nr .btn view:first-child{\r\n\t\tmargin-left: 0;\r\n\t}\r\n\t.zw{\r\n\t\tmargin-top: 30%;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop_order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop_order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974052\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}