{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?572e", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?257b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?b032", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?565b", "uni-app:///pages/pagesMy/withdrawal.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?93c4", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/withdrawal.vue?62a9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "money", "service_fee_rate", "amount", "desc", "isPay", "image_url", "image", "onLoad", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "that", "res", "code", "msg", "uni", "icon", "title", "duration", "catch", "getAmount", "getAll", "addimage", "count", "sizeType", "sourceType", "success", "console", "upLoad", "goPay", "mask", "setTimeout", "url", "goDetails"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6C9uB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAEAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAV;MACA;MACAW;QACA,gBAIAC;UAHAC;UACAd;UACAe;QAEA;UACAH;UACAA;UACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;UACAI;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;QACAL;UACAC;UACAC;UACAC;QACA;QACAP;MACA;QACAA;MACA;IACA;IACA;IACAU;MACA;QACA;MACA;QACAN;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAI;MACA;MACAP;QACAQ;QACAC;QAAA;QACAC;QAAA;QACAC;UACAX;YACAE;UACA;UACA;UACAU;UACAhB;QACA;MACA;IACA;IACAiB;MACA;MACAjB;QACA,IACAE,OAGAD,IAHAC;UACAd,OAEAa,IAFAb;UACAe,MACAF,IADAE;QAEA;UACAC;UACAA;YACAE;YACAD;UACA;UACAL;UACAA;QACA;UACAI;UACAA;YACAE;YACAD;UACA;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;QACA;UACAd;YACAC;YACAC;YACAC;UACA;UACA;QACA;QACA;UACAH;YACAC;YACAC;YACAC;UACA;UACA;QACA;QAEA;QAKA;UACAlB;UACAG;UACAI;QACA;QACAI;UACA,iBAIAC;YAHAC;YACAd;YACAe;UAEA;YACAC;cACAC;cACAC;cACAC;cACAY;YACA;YACAC;cACAhB;gBACAiB;cACA;YACA;UACA;YACAjB;cACAC;cACAC;cACAC;YACA;UACA;QACA,GACAC,sBAEA;MACA;QACAJ;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAe;MACAlB;QACAiB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAA2hC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACA/iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/withdrawal.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/withdrawal.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdrawal.vue?vue&type=template&id=6e2b4c66&\"\nvar renderjs\nimport script from \"./withdrawal.vue?vue&type=script&lang=js&\"\nexport * from \"./withdrawal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdrawal.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/withdrawal.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdrawal.vue?vue&type=template&id=6e2b4c66&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdrawal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdrawal.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"withdrawal\">\r\n\t\t\t<view class=\"withTo\">\r\n\t\t\t\t<view class=\"title\">提现至</view>\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<image src=\"/static/icon_fxwx.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view>微信钱包</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef MP-ALIPAY -->\r\n\t\t\t\t\t<image src=\"/static/icon_zfb.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view>支付宝钱包</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"price\">\r\n\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t<view>可提现余额:</view>\r\n\t\t\t\t\t<text>￥{{money || '0.00'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"getAll\">全部提现</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"inputbox\">\r\n\t\t\t\t<view>￥</view>\r\n\t\t\t\t<input type=\"number\" placeholder=\"请输入提现金额\" placeholder-style=\"color:#C5C5C5\" :disabled=\"!isPay\" v-model=\"amount\" @input=\"getAmount\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"service\">提现手续费：{{service_fee_rate}}%</view>\r\n\t\t</view>\r\n\t\t<view class=\"uplod\">\r\n\t\t\t<view class=\"title\">上传收款码</view>\r\n\t\t\t<view class=\"pic\" @click=\"addimage\">\r\n\t\t\t\t<image :src=\"image_url?image_url:'/static/up-ma.png'\" mode=\"widthFix\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"instruction\">\r\n\t\t\t<view>提现说明</view>\r\n\t\t\t<rich-text :nodes=\"desc\" class=\"xx\"></rich-text>\r\n\t\t</view>\r\n\t\t<view :class=\"isPay?'submit':'submit wu'\" @click=\"goPay\">确认提现</view>\r\n\t\t<view class=\"details\" @click=\"goDetails\" v-if=\"type == 1\">提现明细</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttype: '',// 1业务员 2分销\r\n\t\t\t\tmoney: '', // 可提现金额\r\n\t\t\t\tservice_fee_rate: '', // 手续费\r\n\t\t\t\tamount: '', // 提现金额\r\n\t\t\t\tdesc: '',\r\n\r\n\t\t\t\tisPay: '', // 是否可提现 \r\n\t\t\t\timage_url: '',\r\n\t\t\t\timage: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.type = option.type\r\n\t\t\tthis.getWithdrawal()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 提现数据\r\n\t\t\tgetWithdrawal() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\ttype: that.type\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.w_info(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.money = data.money\r\n\t\t\t\t\t\tthat.service_fee_rate = data.service_fee_rate\r\n\t\t\t\t\t\tthat.desc = data.desc\r\n\t\t\t\t\t\tif (data.money > 0) {\r\n\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.isPay = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取提现金额\r\n\t\t\tgetAmount(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (e.detail.value > that.money) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '超过可提现额度',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.amount = that.money\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.amount = e.detail.value\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 全部\r\n\t\t\tgetAll() {\r\n\t\t\t\tif (this.isPay) {\r\n\t\t\t\t\tthis.amount = this.money\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '无可提现金额',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择图片\r\n\t\t\taddimage(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\t\tsourceType: ['album', 'camera'], //从相册选择\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle:'上传中'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet tempFilePaths = res.tempFilePaths\r\n\t\t\t\t\t\tconsole.log(tempFilePaths)\r\n\t\t\t\t\t\tthat.upLoad(tempFilePaths[0])\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tupLoad(i) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.$util.uploadFile(i).then((res)=>{\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res\r\n\t\t\t\t\tif(code == 1){\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.image_url = data.url\r\n\t\t\t\t\t\tthat.image = data.src\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 支付\r\n\t\t\tgoPay() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (that.isPay) {\r\n\t\t\t\t\tif(!that.amount){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '请输入提现金额',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!that.image){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '请上传收款码',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #ifdef  MP-WEIXIN\r\n\t\t\t\t\tlet type = 'wechat'\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef  MP-ALIPAY\r\n\t\t\t\t\tlet type = 'alipay'\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\ttype: type,\r\n\t\t\t\t\t\tamount: that.amount,\r\n\t\t\t\t\t\timage: that.image,\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$api.withdrawal_apply(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: '提交成功,等待审核!',\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/pagesPay/pay?type=1&status=' + that.type\r\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '无可提现金额',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 提现明细\r\n\t\t\tgoDetails() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: 'withdrawal_details?type=1'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content {\r\n\t\tpadding: 20rpx 24rpx;\r\n\t}\r\n\r\n\t.withdrawal {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 0 24rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.withdrawal .withTo {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 28rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.withdrawal .withTo .title {\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.withdrawal .withTo .nr {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.withdrawal .withTo .nr image {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t}\r\n\r\n\t.withdrawal .withTo .nr view {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\r\n\t.withdrawal .price {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.withdrawal .price .money {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.withdrawal .price .money view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.withdrawal .price .money text {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #FC451E;\r\n\t}\r\n\r\n\t.withdrawal .price .btn {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #3478FB;\r\n\t}\r\n\r\n\t.withdrawal .inputbox {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\t.withdrawal .inputbox view {\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\twidth: 44rpx;\r\n\t}\r\n\r\n\t.withdrawal .inputbox input {\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tcolor: #333;\r\n\t\twidth: calc(100% - 44rpx);\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.withdrawal .service{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t.uplod{\r\n\t\tbackground: #fff;\r\n\t\tpadding: 32rpx 24rpx 40rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t.uplod .title{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 28rpx;\r\n\t}\r\n\t.uplod .pic{\r\n\t\twidth: 200rpx;\r\n\t}\r\n\t.uplod .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t}\r\n\t.instruction{\r\n\t\tmargin-bottom: 170rpx;\r\n\t}\r\n\t.instruction view {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 14rpx;\r\n\t}\r\n\t.instruction .xx {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 38rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.submit {\r\n\t\ttext-align: center;\r\n\t\tmargin: 0 auto 58rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 44rpx;\r\n\t}\r\n\t.submit.wu{\r\n\t\tbackground: #ccc;\r\n\t}\r\n\t.details {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #3478FB;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdrawal.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./withdrawal.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974056\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}