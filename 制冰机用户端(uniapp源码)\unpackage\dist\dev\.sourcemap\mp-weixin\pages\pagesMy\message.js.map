{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?8bff", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?4485", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?2313", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?770f", "uni-app:///pages/pagesMy/message.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?054b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/message.vue?198d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "imageList", "images", "videos", "video", "content", "phone", "onLoad", "uni", "title", "methods", "getcontent", "addimage", "count", "sizeType", "sourceType", "success", "console", "that", "addvideo", "upLoad", "code", "res", "msg", "icon", "previewImage", "arr", "del", "delvideo", "getfeedBack", "time", "setTimeout", "url", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAutB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2B3uB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;MACAC;QACAC;MACA;IACA;MACAD;QACAC;MACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACAJ;QACAK;QACAC;QAAA;QACAC;QAAA;QACAC;UACAR;YACAC;UACA;UACA;UACAQ;UACA;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAX;QACAO;QAAA;QACAC;UACAR;YACAC;UACA;UACAQ;UACAC;QACA;MACA;IACA;IACA;IACAE;MACA;MACAF;QACA,IACAG,OAGAC,IAHAD;UACAtB,OAEAuB,IAFAvB;UACAwB,MACAD,IADAC;QAEA;UACAf;UACAA;YACAC;YACAe;UACA;UACA;YACAN;YACAA;UACA;YACAA;YACAA;UACA;QACA;UACAV;UACAA;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACArB;UACAC;UACAe;QACA;QACA;MACA;MACA;MACA;QACAnB;QACAH;QACAC;QACAH;QACAM;MACA;MACAY;QACA,gBAKAI;UAJAD;UACAtB;UACAwB;UACAO;QAEA;UACAtB;YACAC;YACAe;UACA;UACAO;YACAvB;cACAwB;YACA;UACA;QACA;UACAxB;YACAC;YACAe;UACA;QACA;MACA,GACAS,wBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAAwhC,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACA5iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=314a2bc2&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=314a2bc2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imageList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"message\">\r\n\t\t\t<input type=\"tel\" placeholder=\"请输入联系方式\" v-model=\"phone\">\r\n\t\t\t<textarea class=\"textarea\" value=\"\" placeholder=\"客官~请描述您遇到的问题，您的建议是我们最大的动力，建议上传照片~\" placeholder-style=\"color:#C5C5C5\" @input=\"getcontent\"></textarea>\r\n\t\t\t<view class=\"box-img\">\r\n\t\t\t\t<view class=\"image\" v-for=\"(item,i) in imageList\" :key=\"i\">\r\n\t\t\t\t\t<image class=\"moment_img\" :src=\"item\" @click=\"previewImage(i)\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<image class=\"del\" src=\"/static/icon_delet.png\" @click=\"del(i)\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"image\" v-if=\"imageList.length < 9\">\r\n\t\t\t\t\t<image class=\"moment_img\" src='/static/up-img.png' mode=\"aspectFill\" @click=\"addimage\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"image\" v-if=\"videos\">\r\n\t\t\t\t\t<video :src=\"videos\"></video>\r\n\t\t\t\t\t<image class=\"del\" src=\"/static/icon_delet.png\" mode=\"aspectFit\" @click=\"delvideo\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"image\" v-else>\r\n\t\t\t\t\t<image class=\"moment_img\" src='/static/up-video.png' mode=\"aspectFill\" @click=\"addvideo\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"button\" @click=\"getfeedBack\">提交</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttype: '',// 1意见 2故障\r\n\t\t\t\timageList:[],// 显示用图\r\n\t\t\t\timages:[],// 提交用图\r\n\t\t\t\tvideos:'',// 显示视频\r\n\t\t\t\tvideo:'',// 提交视频\r\n\t\t\t\tcontent: \"\",// 反馈内容\r\n\t\t\t\tphone: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tif(option.type == 1){\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '意见反馈'\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '故障上报'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.type = option.type\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取反馈信息\r\n\t\t\tgetcontent(e){\r\n\t\t\t\tthis.content = e.detail.value\r\n\t\t\t},\r\n\t\t\t// 选择图片\r\n\t\t\taddimage(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet num = 9 - that.imageList.length\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: num,\r\n\t\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\t\tsourceType: ['album', 'camera'], //从相册选择\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle:'上传中'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet tempFilePaths = res.tempFilePaths\r\n\t\t\t\t\t\tconsole.log(tempFilePaths)\r\n\t\t\t\t\t\tfor (var i = 0; i < tempFilePaths.length; i++){\r\n\t\t\t\t\t\t\tthat.upLoad(tempFilePaths[i],1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 选择视频\r\n\t\t\taddvideo(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseVideo({\r\n\t\t\t\t\tsourceType: ['album', 'camera'], //从相册选择\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle:'上传中'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tconsole.log(res.tempFilePath)\r\n\t\t\t\t\t\tthat.upLoad(res.tempFilePath,2)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 上传图片\r\n\t\t\tupLoad(i,t) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.$util.uploadFile(i).then((res)=>{\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res\r\n\t\t\t\t\tif(code == 1){\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif(t == 1){\r\n\t\t\t\t\t\t\tthat.imageList.push(data.url)\r\n\t\t\t\t\t\t\tthat.images.push(data.src)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.videos = data.url\r\n\t\t\t\t\t\t\tthat.video = data.src\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 图片预览\r\n\t\t\tpreviewImage(i){\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tthis.imageList.forEach((el)=>{\r\n\t\t\t\t\tarr.push(el)\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.previewImage(i,arr)\r\n\t\t\t},\r\n\t\t\t// 删除\r\n\t\t\tdel(i){\r\n\t\t\t\tthis.imageList.splice(i,1)\r\n\t\t\t\tthis.images.splice(i,1)\r\n\t\t\t},\r\n\t\t\t// 删除视频\r\n\t\t\tdelvideo(){\r\n\t\t\t\tthis.videos = ''\r\n\t\t\t\tthis.video = ''\r\n\t\t\t},\r\n\t\t\t// 提交\r\n\t\t\tgetfeedBack(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(!that.content){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请填写内容',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet images = that.images.join(\",\")\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tcontent: that.content,\r\n\t\t\t\t\timages: images,\r\n\t\t\t\t\tvideos: that.video,\r\n\t\t\t\t\ttype: that.type,\r\n\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t}\r\n\t\t\t\tthat.$api.feedBack(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: that.type == 1?'意见反馈成功':'故障上报成功',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/my',\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content{\r\n\t\tpadding: 20rpx 24rpx 0;\r\n\t}\r\n\t.message{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 34rpx 20rpx 25rpx;\r\n\t}\r\n\t.message input{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 44rpx;\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.message .textarea{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 340rpx;\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t.message .box-img{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.message .box-img .image{\r\n\t\twidth: 200rpx;\r\n\t\theight: 205rpx;\r\n\t\tposition: relative;\r\n\t\tmargin-top: 25rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t.message .box-img .image:nth-child(3n){\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.message .box-img .image:nth-child(-n + 3){\r\n\t\tmargin-top: 0;\r\n\t}\r\n\t.message .box-img .image .moment_img{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t.message .box-img .image video{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t.message .box-img .image .del{\r\n\t\twidth: 51rpx;\r\n\t\theight: 40rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t}\r\n\t.message .box-img .video{\r\n\t\twidth: 200rpx;\r\n\t\theight: 205rpx;\r\n\t\tposition: relative;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.button{\r\n\t\tmargin-top: 230rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 44rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974045\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}