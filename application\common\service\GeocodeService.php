<?php

namespace app\common\service;

/**
 * 腾讯地图地理编码服务类
 */
class GeocodeService
{
    // 腾讯地图API配置
    private static $apiKey = 'ESUBZ-3EMW7-TAFXF-PKE3L-BKWPO-BSFCJ';
    private static $apiUrl = 'https://apis.map.qq.com/ws/geocoder/v1/';

    /**
     * 根据地址获取经纬度
     * @param string $address 完整地址
     * @param string $region 指定地址所在城市（可选）
     * @return array|false 返回经纬度数组或false
     */
    public static function getLocationByAddress($address, $region = '')
    {
        if (empty($address)) {
            return false;
        }

        // 构建请求参数
        $params = [
            'address' => $address,
            'key' => self::$apiKey,
            'output' => 'json'
        ];

        // 如果指定了城市，添加region参数
        if (!empty($region)) {
            $params['region'] = $region;
        }

        // 构建请求URL
        $url = self::$apiUrl . '?' . http_build_query($params);

        try {
            // 发送HTTP请求
            $response = self::httpGet($url);
            
            if ($response === false) {
                return false;
            }

            // 解析JSON响应
            $data = json_decode($response, true);
            
            if (!$data || $data['status'] !== 0) {
                // 记录错误日志
                \think\Log::error('腾讯地图地理编码API调用失败: ' . ($data['message'] ?? '未知错误'));
                return false;
            }

            // 提取经纬度
            if (isset($data['result']['location'])) {
                $location = $data['result']['location'];
                return [
                    'latitude' => $location['lat'],
                    'longitude' => $location['lng']
                ];
            }

            return false;

        } catch (\Exception $e) {
            \think\Log::error('地理编码服务异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送HTTP GET请求
     * @param string $url 请求URL
     * @return string|false 响应内容或false
     */
    private static function httpGet($url)
    {
        // 使用cURL发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10秒超时
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; GeocodeService/1.0)');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            \think\Log::error('cURL请求错误: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            \think\Log::error('HTTP请求失败，状态码: ' . $httpCode);
            return false;
        }

        return $response;
    }

    /**
     * 批量地理编码（如果需要）
     * @param array $addresses 地址数组
     * @return array 结果数组
     */
    public static function batchGeocode($addresses)
    {
        $results = [];
        foreach ($addresses as $address) {
            $result = self::getLocationByAddress($address);
            $results[] = $result;
        }
        return $results;
    }
}
