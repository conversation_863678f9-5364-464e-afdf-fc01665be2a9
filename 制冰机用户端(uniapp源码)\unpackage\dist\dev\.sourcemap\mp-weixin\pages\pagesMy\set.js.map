{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?8c63", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?cac7", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?e00c", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?b805", "uni-app:///pages/pagesMy/set.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?4d45", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/set.vue?b7ba"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "avatar", "nickname", "mobile", "sexs", "sex", "sex_index", "Ages", "Age_id", "Age", "Age_index", "onShow", "onLoad", "methods", "getUser", "that", "res", "code", "msg", "time", "uni", "icon", "title", "duration", "catch", "getNickname", "onChooseAvatar", "mask", "console", "bindSexChange", "bindAgeChange", "getAge", "getPhoneNumber", "getPhone", "getALphone", "my", "success", "fail", "getALPhones", "edata", "getSubmit", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmtB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2CvuB;EACAC;IACA;MACAC;MAAA;;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA,gBAKAC;UAJAC;UACAlB;UACAmB;UACAC;QAEA;UACAJ;UACAA;UACAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACAC;MACA;IACA;IACAC;MACAN;QACAE;QACAK;MACA;MACAC;MACA;MACAb;QACA,IACAE,OAGAD,IAHAC;UACAlB,OAEAiB,IAFAjB;UACAmB,MACAF,IADAE;QAEA;UACAE;UACAA;YACAE;YACAD;UACA;UACAN;QACA;UACAK;UACAA;YACAE;YACAD;UACA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAhB;QACA,iBAKAC;UAJAC;UACAlB;UACAmB;UACAC;QAEA;UACAJ;QACA;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACAQ;MACA;MACAJ;MACA;QACAX;MACA;MACA;QACAF;MACA;QACAK;UACAE;UACAD;QACA;MACA;IACA;IAEAY;MACA;MACAlB;QACA,iBAIAC;UAHAC;UACAlB;UACAmB;QAEA;UACAE;YACAE;YACAD;YACAE;YACAI;UACA;UACAZ;QACA;UACAK;YACAE;YACAD;UACA;QACA;MACA,GACAG,sBAEA;IACA;IACAU;MACA;MACAC;QACAC;UACAR;UACA;UACAb;QACA;QACAsB;UACAjB;YACAE;YACAD;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;QACAC;MACA;MACAxB;QACA,iBAIAC;UAHAC;UACAlB;UACAmB;QAEA;UACAE;YACAE;YACAD;YACAE;YACAI;UACA;UACAZ;QACA;UACAK;YACAE;YACAD;UACA;QACA;MACA,GACAG,sBAEA;IACA;IAIAgB;MACA;MACA;QACAtC;QACAD;QACA;QACA;MACA;;MACAc;QACA,iBAKAC;UAJAC;UACAlB;UACAmB;UACAC;QAEA;UACAC;YACAC;YACAC;YACAC;YACAI;UACA;UACAc;YACArB;UACA;QACA;UACAA;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClTA;AAAA;AAAA;AAAA;AAAohC,CAAgB,o8BAAG,EAAC,C;;;;;;;;;;;ACAxiC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/set.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/set.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./set.vue?vue&type=template&id=21f780fa&\"\nvar renderjs\nimport script from \"./set.vue?vue&type=script&lang=js&\"\nexport * from \"./set.vue?vue&type=script&lang=js&\"\nimport style0 from \"./set.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/set.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set.vue?vue&type=template&id=21f780fa&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"set\">\r\n\t\t\t<view class=\"avatar\">\r\n\t\t\t\t<view>头像</view>\r\n\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\r\n\t\t\t\t\t<image class=\"avatar\" :src=\"avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view>昵称</view>\r\n\t\t\t\t<input type=\"nickname\" :placeholder=\"nickname\" @input=\"getNickname\" @blur=\"getNickname\"/>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"nr\">\r\n\t\t\t\t<view>性别</view>\r\n\t\t\t\t<picker @change=\"bindSexChange\" :value=\"sex_index\" :range=\"sexs\">\r\n\t\t\t\t\t<text>{{sex?sex:'请选择性别'}}</text>\r\n\t\t\t\t</picker>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <view class=\"nr\">\r\n\t\t\t\t<view>年龄段</view>\r\n\t\t\t\t<picker @change=\"bindAgeChange\" :value=\"Age_index\" range-key=\"name\" :range=\"Ages\">\r\n\t\t\t\t\t<text>{{Age?Age:'请选择年龄段'}}</text>\r\n\t\t\t\t</picker>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view>手机号</view>\r\n\t\t\t\t<view v-if=\"mobile\">{{mobile}}</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t<button open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" v-else>绑定手机号</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\r\n\t\t\t\t<!-- #ifdef MP-ALIPAY -->\r\n\t\t\t\t<button open-type=\"getAuthorize\" @getAuthorize=\"getALphone\" onError=\"onAuthError\" scope='phoneNumber' v-else>绑定手机号</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @click=\"getSubmit\">保存</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\tavatar: '',\r\n\t\t\t\tnickname: '',\r\n\t\t\t\tmobile: '',\r\n\t\t\t\t\r\n\t\t\t\tsexs: ['男','女','未知'],\r\n\t\t\t\tsex: '',\r\n\t\t\t\tsex_index: '',\r\n\t\t\t\tAges: [],\r\n\t\t\t\tAge_id: '',\r\n\t\t\t\tAge: '',\r\n\t\t\t\tAge_index: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// this.getAge()\r\n\t\t\tthis.getUser()\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 个人资料\r\n\t\t\tgetUser(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.userInfo(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.nickname = data.user.nickname\r\n\t\t\t\t\t\tthat.mobile = data.user.mobile\r\n\t\t\t\t\t\tthat.avatar = data.user.avatar\r\n\t\t\t\t\t\t// that.sex = data.user.gender\r\n\t\t\t\t\t\t// that.sexs.forEach((el,i) => {\r\n\t\t\t\t\t\t// \tif(i == data.user.gender){\r\n\t\t\t\t\t\t// \t\tthat.sex_index = i\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t// that.Age_id = data.user.age_id\r\n\t\t\t\t\t\t// that.Ages.forEach((el,i) => {\r\n\t\t\t\t\t\t// \tif(el.id == data.user.age_id){\r\n\t\t\t\t\t\t// \t\tthat.Age = el.name\r\n\t\t\t\t\t\t// \t\tthat.Age_index = i\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetNickname(e){\r\n\t\t\t\tthis.nickname = e.detail.value\r\n\t\t\t},\r\n\t\t\tonChooseAvatar(e){\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.$util.uploadFile(e.detail.avatarUrl).then((res)=>{\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res\r\n\t\t\t\t\tif(code == 1){\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.avatar = data.url\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 性别选择\r\n\t\t\tbindSexChange(e){\r\n\t\t\t\tthis.sex_index = e.detail.value\r\n\t\t\t\tthis.sex = this.sexs[e.detail.value]\r\n\t\t\t},\r\n\t\t\t// 年龄段\r\n\t\t\tbindAgeChange(e){\r\n\t\t\t\tthis.Age_index = e.detail.value\r\n\t\t\t\tthis.Age_id = this.Ages[e.detail.value].id\r\n\t\t\t\tthis.Age = this.Ages[e.detail.value].name\r\n\t\t\t},\r\n\t\t\tgetAge(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {}\r\n\t\t\t\tthat.$api.ageList(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.Ages = data\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tcode: e.detail.code,\r\n\t\t\t\t}\r\n\t\t\t\tif (e.detail.errMsg == 'getPhoneNumber:ok') {\r\n\t\t\t\t\tthat.getPhone(params)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"手机号绑定失败\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tgetPhone(params) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$api.getPhone(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定成功\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.getUser()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetALphone(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tmy.getPhoneNumber({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(JSON.parse(res.response).response)\r\n\t\t\t\t\t\tlet encryptedData = JSON.parse(res.response).response;\r\n\t\t\t\t\t\tthat.getALPhones(encryptedData)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetALPhones(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tedata: e,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.getALPhone(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定成功\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.getUser()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tgetSubmit(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tnickname: that.nickname,\r\n\t\t\t\t\tavatar: that.avatar,\r\n\t\t\t\t\t// age_id: that.Age_id,\r\n\t\t\t\t\t// gender: that.sex,\r\n\t\t\t\t}\r\n\t\t\t\tthat.$api.modifyUser(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '修改成功',\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content{\r\n\t\tpadding: 26rpx;\r\n\t}\r\n\t.set{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 178rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t.set .avatar{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.set .avatar view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 136rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.set .avatar button{\r\n\t\twidth: 84rpx;\r\n\t\theight: 84rpx;\r\n\t\tborder-radius: 50%;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t\tbackground: none;\r\n\t\tborder: none;\r\n\t}\r\n\t.set .avatar button::after{\r\n\t\tborder: none;\r\n\t}\r\n\t.set .avatar button image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.set .nr{\r\n\t\tborder-top: 1rpx solid #E6E6E6;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.set .nr view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 102rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.set .nr input{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 102rpx;\r\n\t\tcolor: #333;\r\n\t\twidth: 80%;\r\n\t\theight: 102rpx;\r\n\t\ttext-align: right;\r\n\t}\r\n\t.set .nr button{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 102rpx;\r\n\t\tcolor: #333;\r\n\t\twidth: 80%;\r\n\t\theight: 102rpx;\r\n\t\ttext-align: right;\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t\tbackground: none;\r\n\t\tborder: none;\r\n\t}\r\n\t.set .nr button::after{\r\n\t\tborder: none;\r\n\t}\r\n\t.btn{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 42rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974038\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}