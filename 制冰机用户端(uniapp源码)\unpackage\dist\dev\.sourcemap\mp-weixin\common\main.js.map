{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/App.vue?30c6", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/App.vue?4691", "uni-app:///App.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/App.vue?0ce7", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/App.vue?0874", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?3aa7", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?d425", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?8bfa", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?031e", "uni-app:///components/wx-login/wx-login.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?b3b9", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/components/wx-login/wx-login.vue?ff42"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$util", "util", "$cache", "cache", "$http", "http", "$api", "api", "$store", "Vuex", "component", "login", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "console", "onShow", "uni", "success", "onHide", "data", "code", "encryptedData", "iv", "user_oauth_id", "isShow", "isPhone", "avatar", "nick<PERSON><PERSON>", "props", "show", "type", "default", "onLoad", "methods", "tologin", "close", "that", "provider", "desc", "fail", "title", "icon", "onGetAuthorize", "my", "scopes", "onAuthError", "getcode", "catch", "getlogin", "edata", "checkBindMobile", "closePhoneNumber", "getPhoneNumber", "getPhone", "getALPhones"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AAEA;AAYA;AAAqB;AAAA;AAnBrB;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAMvB;;AAGpCC,YAAG,CAACC,SAAS,CAACC,KAAK,GAAGC,aAAI;AAC1BH,YAAG,CAACC,SAAS,CAACG,MAAM,GAAGC,cAAK;AAC5BL,YAAG,CAACC,SAAS,CAACK,KAAK,GAAGC,aAAI;AAC1BP,YAAG,CAACC,SAAS,CAACO,IAAI,GAAGC,YAAG;AACxBT,YAAG,CAACC,SAAS,CAACS,MAAM,GAAGC,cAAI;AAE3BX,YAAG,CAACY,SAAS,CAAC,UAAU,EAAEC,KAAK,CAAC;AAIhCb,YAAG,CAACc,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIlB,YAAG,mBACZgB,YAAG,EACR;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1BZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAqrB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;eCCzsB;EACAC;IACAC;EACA;EACAC;IAAA;IACAD;IACAE;MACAC;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;MAaA;IACA;EACA;;EACAC;IACAJ;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAA0+B,CAAgB,o8BAAG,EAAC,C;;;;;;;;;;ACA9/B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAwtB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqC5uB;EACAK;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACA7B;MACA;MACA8B;MACA;MACApB;QACAqB;QACApB;UACA;YACAmB;UACA;QACA;MACA;MACA;MACApB;QACAsB;QACArB;UACAH;UACAsB;UACAA;UACAA;UACAA;UACAA;QACA;QACA;QACAG;UACAvB;YACAwB;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACAC;QACAJ;UACAzB;QACA;QACAG;UACA;UACAH;UACA;UACA;UACA6B;YACAC;YACA3B;cACAH;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACA+B;MACA7B;QACAwB;MACA;MACA;IACA;IACA;IACAM;MACA;MACA;QACA1B;QACAM;QACAC;MACA;MAEAS;QACA;UACA;UACA;UACAA;UACAA;QACA;MACA,GACAW,sBAEA;IAgBA;IACAC;MACA;MACA;QACAC;QACA3B;QACAC;QACA;QACA;MACA;;MACAa;QACA;UACA;UACAA;UAEAA;UACAA;UACAA;QACA;MACA,GACAW,sBAEA;IACA;IACA;IACAG;MACA;MACA;MACAd;QACA;UACA;YACAA;YACAA;YACAA;UACA;YACAA;UACA;QACA;MACA,GACAW,sBAEA;IACA;IACA;IACAI;MACA;IACA;IACA;IAEAC;MACA;MACAtC;MACA;QACA;QACAsB;QACAA;QACAA;MACA;QACAA;QACApB;UACAwB;UACAC;QACA;MACA;IACA;IAwBAY;MACA;MACA;QACAjC;MACA;MACAgB;QACAtB;QACA;UACAsB;UACApB;YACAwB;YACAC;UACA;UACAL;QACA;UACAA;UACApB;YACAwB;YACA;YACAC;UACA;QACA;MACA,GACAM,sBAEA;IACA;IAEAO;MACA;MACA;QACAL;MACA;MACAb;QACAtB;QACA;UACAsB;UACApB;YACAwB;YACAC;UACA;UACAL;QACA;UACAA;UACApB;YACAwB;YACAC;UACA;QACA;MACA,GACAM,sBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvTA;AAAA;AAAA;AAAA;AAAyhC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;ACA7iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from 'App'\r\nimport util from \"./common/util.js\"   // 常用方法\r\nimport cache from \"./common/cache.js\" // 缓存\r\nimport http from 'http/http.js'  //http 请求\r\nimport api from 'http/api.js'  //api\r\nimport login from '@/components/wx-login/wx-login.vue'  //api\r\nimport Vuex from \"store/index.js\"   // vuex\r\n\r\n\r\nVue.prototype.$util = util\r\nVue.prototype.$cache = cache\r\nVue.prototype.$http = http;\r\nVue.prototype.$api = api;\r\nVue.prototype.$store = Vuex\r\n\r\nVue.component('wx-login', login)\r\n\r\n\r\nimport Vue from 'vue'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n    ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tlet statusBarHeight = res.statusBarHeight\r\n\t\t\t\t\tthis.$cache.updateCache('statusBarHeight', statusBarHeight) // 手机状态栏的高度\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect() // 胶囊\r\n\t\t\t\t\tconst navBarPadding = (menuButton.top - res.statusBarHeight) * 2\r\n\t\t\t\t\tlet navHeight = menuButton.height + navBarPadding\r\n\t\t\t\t\tlet headerHeight = navHeight + statusBarHeight\r\n\t\t\t\t\tthis.$cache.updateCache('headerHeight', headerHeight)\r\n\t\t\t\t\tthis.$cache.updateCache('navHeight', navHeight) // 头部高度\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\tthis.$cache.updateCache('navHeight', res.titleBarHeight)\r\n\t\t\t\t\tthis.$cache.updateCache('headerHeight', statusBarHeight + res.titleBarHeight)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tthis.statusBarHeight = res.statusBarHeight // 屏幕边界到安全区域的距离\r\n\t\t\t\t\tthis.navHeight = 44 // 标题栏高度\r\n\t\t\t\t\tthis.headerHeight = res.statusBarHeight + 44 // 标题栏高度\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\tpage {\r\n\t\tbackground: #F2F2F2;\r\n\t}\r\n\r\n\tuni-tabbar {\r\n\t\tz-index: 5;\r\n\t}\r\n\t\r\n\t[v-cloak] {\r\n\t    display: none\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: #F2F2F2;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.heard{\r\n\t\twidth: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tz-index: 5;\r\n\t}\r\n\t.heard .back{\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\twidth: 80rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAdCAYAAACjbey/AAAAAXNSR0IArs4c6QAAAO5JREFUSEu1lUEOgjAQRf8/gPfyMJq4EBNcwQJiAju9jLdSLzAGQpNSaqcjyrLJ+/M7/TMQX3wi0gIoATS08iLSADhP3MMk4FV2dfMdiEgH4Og5vpHcZzn4BA9iqkAKVgU0OCkgIlcAO+/OPckifLXoFXLhqAMLvBCwwjOBCFyRrLWkjj2IwAXJXoNHB0G2h7OS5CUHdgIvAJsJuJPc5sJOwI2m42wOVvfAlf22kbMkRkRqklWqJ4soW8P0+1lI9CR/Gj2RcI0tRP67kRJOxoWqrjT/+VZtZc9JGPtW7UEYouDn8jQLTLNzAnAA0L0BTv+O6Zsgr2wAAAAASUVORK5CYII=) no-repeat center;\r\n\t\tbackground-size: 20rpx auto;\r\n\t\tz-index: 1;\r\n\t\t/*  #ifdef  MP-ALIPAY  */\r\n\t\tdisplay: none;\r\n\t\t/*  #endif  */\r\n\t}\r\n\t.heard.on .back{\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAjCAYAAACU9ioYAAAAAXNSR0IArs4c6QAAANVJREFUSEu91tsNgzAMBVAzRufIYNAB+tcB6GDOHGwRBFJQCInjl8oAR9dgG09gfEIIMwCsALAg4m+yeAWWmUUNNrATVYE97ChbDFKY+B2OMFHJHIwNcjEWSGBfRPzUbUd+lB6WUlpjjO9WD3dBDdYtWYs1QQv2AK3YDfTALtALO0FPLIOp0U8bIr40u9I/4ZHCs+xrUrzQ2+h5oI9ZtqLN5WBB/7Ntcv9pkg7/eq4be5Q0nx/lRA0TSlE2SE1UmVQEclAxOEJVIIWqwQ4qv77qpVtfsDvIPsx0gE5g+QAAAABJRU5ErkJggg==) no-repeat center;\r\n\t\tbackground-size: 20rpx auto;\r\n\t}\r\n\t.heard .title {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 34rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex: 1;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: #fff;\r\n\t}\r\n\t.heard.on .title{\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\r\n\t.butn_hover {\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.zw{\r\n\t\tpadding-top: 30%;\r\n\t}\r\n\t.zw image {\r\n\t\twidth: 334rpx;\r\n\t\theight: auto;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto 20rpx;\r\n\t}\r\n\r\n\t.zw view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.deitor_pic{\r\n\t\tmax-width: 100% !important;\r\n\t\theight: auto;\r\n\t}\r\n\t\r\n\t\r\n\t/*  #ifdef  MP-ALIPAY  */\r\n\t.wx_login {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\t/*  #endif  */\r\n</style>\n", "import mod from \"-!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291975644\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./wx-login.vue?vue&type=template&id=70e556a4&\"\nvar renderjs\nimport script from \"./wx-login.vue?vue&type=script&lang=js&\"\nexport * from \"./wx-login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wx-login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/wx-login/wx-login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-login.vue?vue&type=template&id=70e556a4&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uni-popup ref=\"popup\" type=\"bottom\" class=\"tc\">\r\n\t\t<view class=\"wx_login\">\r\n\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t<button class=\"xx\" type=\"sumbit\" plain=\"true\" open-type=\"getUserInfo\" @tap=\"login\" v-if=\"isShow\">\r\n\t\t\t\t<!-- <image src=\"/static/icon_fxwx.png\" mode=\"aspectFit\"></image> -->\r\n\t\t\t\t<view>微信授权登录</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\r\n\t\t\t<!-- #ifdef MP-ALIPAY -->\r\n\t\t\t<button class=\"xx\" type=\"primary\" size=\"default\" open-type=\"getAuthorize\" scope=\"userInfo\" @getAuthorize=\"onGetAuthorize\" @error=\"onAuthError\" v-if=\"isShow\">\r\n\t\t\t\t<!-- <image src=\"/static/icon_zfb.png\" mode=\"aspectFit\"></image> -->\r\n\t\t\t\t<view>支付宝授权登录</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t<button open-type=\"getPhoneNumber\" @click=\"closePhoneNumber\" @getphonenumber=\"getPhoneNumber\" class=\"xx\" v-if=\"isPhone\">\r\n\t\t\t\t<!-- <image src=\"/static/icon_ph.png\" mode=\"aspectFit\"></image> -->\r\n\t\t\t\t<view>绑定手机号</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP-ALIPAY -->\r\n\t\t\t<button open-type=\"getAuthorize\" @getAuthorize=\"getALphone\" onError=\"onAuthError\" scope='phoneNumber' class=\"xx\" v-if=\"isPhone\">\r\n\t\t\t\t<!-- <image src=\"/static/icon_ph.png\" mode=\"aspectFit\"></image> -->\r\n\t\t\t\t<view>绑定手机号</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view class=\"btn\" @click=\"close\">取消</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcode: '',\r\n\t\t\t\tencryptedData: '',\r\n\t\t\t\tiv: '',\r\n\t\t\t\tuser_oauth_id:'',\r\n\t\t\t\tisShow: true,\r\n\t\t\t\tisPhone: false,\r\n\t\t\t\t\r\n\t\t\t\tavatar: '',\r\n\t\t\t\tnickName: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttologin(e) {\r\n\t\t\t\tif(e){\r\n\t\t\t\t\tthis.isShow = false\r\n\t\t\t\t\tthis.isPhone = true\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs['popup'].open()\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$refs['popup'].close();\r\n\t\t\t},\r\n\t\t\t// 登录部分\r\n\t\t\tlogin() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$refs['popup'].close();\r\n\t\t\t\t//获取成功基本资料后开启登录，基本资料首先要授权\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.errMsg == \"login:ok\") {\r\n\t\t\t\t\t\t\tthat.code = res.code;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// 获取code 小程序专有，用户登录凭证。\r\n\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\tdesc: \"获取用户基本资料\",\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tthat.encryptedData = res.encryptedData;\r\n\t\t\t\t\t\tthat.iv = res.iv;\r\n\t\t\t\t\t\tthat.avatar = res.userInfo.avatar;\r\n\t\t\t\t\t\tthat.nickName = res.userInfo.nickName;\r\n\t\t\t\t\t\tthat.getcode();\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 用户取消登录后的提示\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"授权登录失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 授权成功回调\r\n\t\t\tonGetAuthorize() {\r\n\t\t\t\tmy.getOpenUserInfo({\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tconst userInfo = JSON.parse(res.response).response; // 以下方的报文格式解析两层 response\r\n\t\t\t\t\t\tconsole.log('支付宝用户信息',userInfo)\r\n\t\t\t\t\t\tthis.nickName = userInfo.nickName\r\n\t\t\t\t\t\tthis.avatar = userInfo.avatar\r\n\t\t\t\t\t\tmy.getAuthCode({\r\n\t\t\t\t\t\t\tscopes: 'auth_base',\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tconsole.log(res.authCode)\r\n\t\t\t\t\t\t\t\tthis.code = res.authCode;\r\n\t\t\t\t\t\t\t\tthis.getcode();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 授权失败回调\r\n\t\t\tonAuthError() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: \"取消授权登录\",\r\n\t\t\t\t});\r\n\t\t\t\tthis.$refs['popup'].close();\r\n\t\t\t},\r\n\t\t\t// 授权登录\r\n\t\t\tgetcode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tcode: that.code,\r\n\t\t\t\t\tavatar: that.avatar,\r\n\t\t\t\t\tnickName: that.nickName,\r\n\t\t\t\t};\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthat.$api.codeWechat(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t// that.session_key = res.data.data.session_key;\r\n\t\t\t\t\t\t// that.openid = res.data.data.openid;\r\n\t\t\t\t\t\tthat.user_oauth_id = res.data.data.user_oauth_id\r\n\t\t\t\t\t\tthat.getlogin()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\tthat.$api.codeAlipay(params).then(res => {\r\n\t\t\t\t\tconsole.log('支付宝登录',res)\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.$refs['popup'].close();\r\n\t\t\t\t\t\tthat.$cache.updateCache('token', res.data.data.token);\r\n\t\t\t\t\t\tthat.$store.commit('logins',true)\r\n\t\t\t\t\t\tthat.checkBindMobile()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetlogin() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tedata: that.encryptedData,\r\n\t\t\t\t\tiv: that.iv,\r\n\t\t\t\t\tuser_oauth_id: that.user_oauth_id,\r\n\t\t\t\t\t// sessionKey: that.session_key,\r\n\t\t\t\t\t// openid: that.openid,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.getToken(params).then(res => {\r\n\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\t// 保存token到本地\r\n\t\t\t\t\t\t\tthat.$cache.updateCache('token', res.data.data.token);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.$refs['popup'].close();\r\n\t\t\t\t\t\t\tthat.$store.commit('logins',true)\r\n\t\t\t\t\t\t\tthat.checkBindMobile()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 判断是否绑定手机号 \r\n\t\t\tcheckBindMobile() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.checkBindMobile(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tif (res.data.data.is_bind == 0) {\r\n\t\t\t\t\t\t\tthat.isShow = false;\r\n\t\t\t\t\t\t\tthat.isPhone = true;\r\n\t\t\t\t\t\t\tthat.$refs['popup'].open();\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.$store.commit('phones',true)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 关闭绑定弹层\r\n\t\t\tclosePhoneNumber(){\r\n\t\t\t\tthis.$refs['popup'].close();\r\n\t\t\t},\r\n\t\t\t// 绑定手机号 \r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tif (e.detail.errMsg == 'getPhoneNumber:ok') {\r\n\t\t\t\t\t// that.getPhone(e.detail.encryptedData,e.detail.iv)\r\n\t\t\t\t\tthat.isPhone = false;\r\n\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\tthat.getPhone(e.detail.code)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"手机号绑定失败\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\tgetALphone(){\r\n\t\t\t\tmy.getPhoneNumber({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tconsole.log(JSON.parse(res.response).response)\r\n\t\t\t\t\t\tlet encryptedData = JSON.parse(res.response).response;\r\n\t\t\t\t\t\tthis.isPhone = false;\r\n\t\t\t\t\t\tthis.closePhoneNumber()\r\n\t\t\t\t\t\tthis.getALPhones(encryptedData)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tthis.closePhoneNumber()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tgetPhone(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tcode: e,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.getPhone(params).then(res => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"手机号绑定成功\",\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthat.$store.commit('phones',true)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\t// title: \"手机号绑定失败\",\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tgetALPhones(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tedata: e,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.getALPhone(params).then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定成功\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.$store.commit('phones',true)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.closePhoneNumber()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"手机号绑定失败\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.wx_login {\r\n\t\tpadding: 50rpx 30rpx 0;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 6;\r\n\t}\r\n\r\n\t.wx_login .xx {\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tbackground: none;\r\n\t\tpadding: 0;\r\n\t\tborder-radius: 0;\r\n\t\tborder: none;\r\n\t\theight: 118rpx;\r\n\t}\r\n\r\n\t.wx_login .xx::after {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.wx_login .xx image {\r\n\t\twidth: 54rpx;\r\n\t\theight: 54rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.wx_login .xx view {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.wx_login .btn {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 86rpx;\r\n\t\tcolor: #999;\r\n\t\tborder-top: 1rpx solid #ccc;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974012\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}