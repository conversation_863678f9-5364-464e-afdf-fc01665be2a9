{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?4793", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?910b", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?aa14", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?7aab", "uni-app:///pages/pagesHome/integral_list.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?8d77", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/integral_list.vue?5301"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "navHeight", "statusBarHeight", "headerHeight", "api", "type", "order", "search", "scrollTitle", "title", "list", "page", "limit", "loading", "onLoad", "methods", "getType", "getList", "name", "that", "setTimeout", "uni", "icon", "duration", "catch", "goDetails", "url", "getSearch", "onReachBottom", "onPullDownRefresh", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyCjvB;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC,cACA;QACAH;QACAI;QACAH;MACA,GACA;QACAD;QACAI;QACAH;MACA,GACA;QACAD;QACAI;QACAH;MACA,EACA;MAEAI;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACAZ;QACAD;QACAM;QACAC;MACA;MACAO;QACAC;UACAC;QACA;QACA;UACA;YACAF;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACAE;YACAC;YACAb;YACAc;UACA;QACA;MACA,GACAC;QACAJ;UACAC;QACA;MACA;MACAD;QACAC;MACA;IACA;IACA;IACAI;MACAJ;QACAK;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAR;QACAZ;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAqB;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAA8hC,CAAgB,88BAAG,EAAC,C;;;;;;;;;;;ACAljC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/integral_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/integral_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./integral_list.vue?vue&type=template&id=5fd38fc8&\"\nvar renderjs\nimport script from \"./integral_list.vue?vue&type=script&lang=js&\"\nexport * from \"./integral_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./integral_list.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/integral_list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral_list.vue?vue&type=template&id=5fd38fc8&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral_list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"heard\" :style=\"{paddingTop: statusBarHeight + 'px'}\">\r\n\t\t\t<view class=\"back\" :style=\"{height: navHeight + 'px',top: statusBarHeight + 'px'}\" @click=\"goBack\"></view>\r\n\t\t\t<view class=\"title\" :style=\"{height: navHeight + 'px'}\">积分商城</view>\r\n\t\t\t<view class=\"search\">\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t\t<image src=\"/static/icon_search.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入搜索内容\" confirm-type='搜索' v-model=\"search\" @confirm='getSearch' />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view scroll-x=\"true\" class=\"scroll\">\r\n\t\t\t\t<view class=\"scroll-item\" v-for=\"(item,index) in scrollTitle\" :class=\"type == item.type?'on':''\" @click=\"getType(item.type,item.order)\">{{item.title}}</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"list\" :style=\"{paddingTop: 'calc(' + headerHeight + 'px' + ' + 182rpx)'}\" v-if=\"list.length > 0\">\r\n\t\t\t<view class=\"nr\" @click=\"goDetails(item.id)\" v-for=\"(item,index) in list\">\r\n\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t<image :src=\"api + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"price\">{{item.score_price}}积分</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\t\r\n\t\t<uni-load-more :status=\"loading\" v-if=\"list.length > 0\"></uni-load-more>\r\n\t\t\r\n\t\t<view class=\"zw\" v-else>\r\n\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view>暂无积分商品</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavHeight: this.$cache.fetchCache('navHeight'),\r\n\t\t\t\tstatusBarHeight: this.$cache.fetchCache('statusBarHeight'),\r\n\t\t\t\theaderHeight: this.$cache.fetchCache('headerHeight'),\r\n\t\t\t\t\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\ttype: '1',// 分类\r\n\t\t\t\torder: '',// 排序方式 ：createtime新品,shop_sales销量\r\n\t\t\t\tsearch: '',// 搜索内容\r\n\t\t\t\t\r\n\t\t\t\tscrollTitle:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: '1',\r\n\t\t\t\t\t\ttitle: '综合',\r\n\t\t\t\t\t\torder: '',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: '2',\r\n\t\t\t\t\t\ttitle: '销量',\r\n\t\t\t\t\t\torder: 'shop_sales',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: '3',\r\n\t\t\t\t\t\ttitle: '新品',\r\n\t\t\t\t\t\torder: 'createtime',\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\t\r\n\t\t\t\tlist: [],// 商品列表\r\n\t\t\t\t\r\n\t\t\t\tpage: 1, // 分页\r\n\t\t\t\tlimit: 12,// 每页条数\r\n\t\t\t\tloading: \"noMore\", // noMore-无更多数据 loading-加载中 more-上拉加载更多\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 筛选\r\n\t\t\tgetType(t,o){\r\n\t\t\t\tthis.type = t;\r\n\t\t\t\tthis.order = o;\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 积分商品列表\r\n\t\t\tgetList(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tname: that.search,\r\n\t\t\t\t\torder: that.order,\r\n\t\t\t\t\ttype: 'score',\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.scoreList(params).then(res => {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tif (that.page == 1) {\r\n\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (var i = 0; i < res.data.data.list.length; i++) {\r\n\t\t\t\t\t\t\tthat.list.push(res.data.data.list[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.data.data.list.length <= that.limit) {\r\n\t\t\t\t\t\t\tthat.loading = 'noMore';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.stopPullDownRefresh(); //得到数据后停止下拉刷新\r\n\t\t\t\t}, 300);\r\n\t\t\t},\r\n\t\t\t// 积分商品详情\r\n\t\t\tgoDetails(e){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'integral_details?id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//  搜索\r\n\t\t\tgetSearch(event){\r\n\t\t\t\tthis.search = event.target.value;\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上拉加载\r\n\t\t\tonReachBottom() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 下拉刷新\r\n\t\t\tonPullDownRefresh() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.list = [];\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回\r\n\t\t\tgoBack(){\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.heard{\r\n\t\tbackground: #F6F6F6 url(data:image/png;base64,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) no-repeat top center;\r\n\t\tbackground-size: 100% auto;\r\n\t}\r\n\r\n\t.search{\r\n\t\tpadding: 0 24rpx 10rpx;\r\n\t}\r\n\t.search .nr{\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 36rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t.search .nr .btn{\r\n\t\twidth: 68rpx;\r\n\t\theight: 72rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.search .nr image{\r\n\t\twidth: 28rpx;\r\n\t\theight: auto;\r\n\t}\r\n\t.search .nr input{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #999;\r\n\t\twidth: calc(100% - 68rpx);\r\n\t\theight: 72rpx;\r\n\t\tpadding: 0;\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.scroll {\r\n\t\tbackground-color: #fff;\r\n\t\twhite-space: nowrap;/*必须要有，规定段落中的文本不进行换行*/\r\n\t\tpadding: 0 24rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.scroll-item {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: inline-block;/*必须要有*/\r\n\t\tmargin-right: 100rpx;\r\n\t}\r\n\t.scroll-item:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t\r\n\t.scroll .scroll-item.on{\r\n\t\tcolor: #3478FB;\r\n\t}\r\n\t\r\n\t.list{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 24rpx;\r\n\t}\r\n\t.list .nr{\r\n\t\twidth: 32%;\r\n\t\tborder-radius: 10rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 2%;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.list .nr:nth-child(3n){\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.list .nr .pic{\r\n\t\twidth: 100%;\r\n\t\theight: 310rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.list .nr .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\t.list .nr .xx{\r\n\t\tpadding: 10rpx 16rpx 20rpx;\r\n\t}\r\n\t.list .nr .xx .name{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #454555;\r\n\t\tpadding: 0 5rpx;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-bottom: 28rpx;\r\n\t}\r\n\t.list .nr .xx .price{\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.zw{\r\n\t\tpadding-top: 50%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral_list.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral_list.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974024\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}