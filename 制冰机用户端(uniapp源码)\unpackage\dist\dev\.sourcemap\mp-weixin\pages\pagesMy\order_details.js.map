{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?f8d4", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?0ca3", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?1b3e", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?1a5a", "uni-app:///pages/pagesMy/order_details.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?6ad4", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/order_details.vue?4243"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "type", "id", "infor", "kefu_tel", "onLoad", "methods", "getUser", "that", "uni", "icon", "title", "duration", "catch", "getDetails", "order_id", "getCancel", "content", "success", "console", "getComplete", "goPay", "url", "getCopy", "openShow", "getClose", "goMore", "getCall", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+IjvB;EACAC;IACA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA;UACAA;QACA;UACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;QACA;QACAP;UACA;YACAA;UACA;YACAC;cACAC;cACAC;cACAC;YACA;UACA;QACA,GACAC,sBAEA;MACA;QACA;UACAX;QACA;QACAM;UACA;YACAA;UACA;YACAC;cACAC;cACAC;cACAC;YACA;UACA;QACA,GACAC,sBAEA;MACA;IACA;IAEA;IACAG;MACA;MACAP;QACAE;QACAM;QACAC;UACA;YACA;cACAH;YACA;YACAP;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAJ;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;YACA,GACAC,sBAEA;UACA;YACAM;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAX;QACAE;QACAM;QACAC;UACA;YACA;cACAhB;YACA;YACAM;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAJ;cACA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;YACA,GACAC,sBAEA;UACA;YACAM;UACA;QACA;MACA;IACA;IACA;IACAE;MACA;MACA;QACAZ;UACAa;QACA;MACA;QACAb;UACAa;QACA;MACA;IACA;IACA;IACAC;MACAd;QACAV;QAAA;QACAmB;UACAT;YACAE;YACAD;UACA;QACA;MACA;IACA;IAEA;IACAc;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAjB;QACAa;MACA;IACA;IACA;IACAK;MACA;MACAlB;QACAmB;MACA,uBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzVA;AAAA;AAAA;AAAA;AAA8hC,CAAgB,88BAAG,EAAC,C;;;;;;;;;;;ACAljC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/order_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/order_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_details.vue?vue&type=template&id=cc47312e&\"\nvar renderjs\nimport script from \"./order_details.vue?vue&type=script&lang=js&\"\nexport * from \"./order_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_details.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/order_details.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_details.vue?vue&type=template&id=cc47312e&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_details.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<image src=\"/static/order_top.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == '0'\">\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>待付款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == 1\">\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>{{type == 1?'已支付':'待发货'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == 2\">\r\n\t\t\t\t<image src=\"/static/icon_order1.png\" mode=\"aspectFit\" v-if=\"type == 1\"></image>\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\" v-if=\"type == 2\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>{{type == 1?'已核销':'待收货'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == 3\">\r\n\t\t\t\t<image src=\"/static/icon_order1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>订单完成</view>\r\n\t\t\t\t\t<text>感谢您对我们的信任，期待您的再次光临</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"type == 1 && infor.status == 4\">\r\n\t\t\t\t<image src=\"/static/icon_order1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>已退款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"type == 1 && infor.status == 5\">\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>待领取</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"type == 1 && infor.status == 6\">\r\n\t\t\t\t<image src=\"/static/icon_order1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>已领取</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == '-1'\">\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>已取消</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nr\" v-if=\"infor.status == '-2'\">\r\n\t\t\t\t<image src=\"/static/icon_order2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>交易关闭</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"order\" :class=\"type == 2?'active':''\">\r\n\t\t\t<view class=\"address\" v-if=\"type == 2 && infor.dispatch_address\">\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view class=\"name\">\r\n\t\t\t\t\t\t<view>{{infor.username}}</view>\r\n\t\t\t\t\t\t<view>{{infor.mobile}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dz\">{{infor.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product\">\r\n\t\t\t\t<view class=\"nr\" v-for=\"(item,index) in infor.goods\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image :src=\"api + item.goods_image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t<view class=\"name\">{{type == 1?item.category_name + item.goods_name:item.goods_name}}</view>\r\n\t\t\t\t\t\t<view class=\"num\">数量：{{item.goods_num}}</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"infor.type == 'store'\">{{item.goods_price}}积分</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-else>￥{{item.goods_price}}</view>\r\n\t\t\t\t\t\t<view class=\"more\" v-if=\"type == 1\" @click=\"goMore\">查看附近设备</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"instruction\" v-if=\"type == 1 && (infor.status == 1 || infor.status == 5 || infor.status == 6)\" @click=\"openShow\">使用须知</view>\r\n\t\t\t\t<view class=\"ma\" v-if=\"type == 1 && (infor.status == 1 || infor.status == 5 || infor.status == 6)\">\r\n\t\t\t\t\t<image :src=\"infor.qrcode\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<view>核销码</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"money\">\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view>商品总额</view>\r\n\t\t\t\t\t<text v-if=\"infor.type == 'store'\">{{infor.total_amount || '0.00'}}积分</text>\r\n\t\t\t\t\t<text v-else>￥{{infor.total_amount || '0.00'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nr\" v-if=\"infor.type != 'store'\">\r\n\t\t\t\t\t<view>优惠金额</view>\r\n\t\t\t\t\t<text>-￥{{infor.coupon_amount || '0.00'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view>实付款：</view>\r\n\t\t\t\t\t<text v-if=\"infor.type == 'store'\">{{infor.pay_fee || '0.00'}}积分</text>\r\n\t\t\t\t\t<text v-else>￥{{infor.pay_fee || '0.00'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order_xx\">\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view>订单编号: {{infor.order_sn}}</view>\r\n\t\t\t\t\t<view class=\"btn\" @click=\"getCopy(infor.order_sn)\">复制</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<view>下单时间：{{infor.createtime || ''}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nr\" v-if=\"infor.status != 0\">\r\n\t\t\t\t\t<view>支付方式：{{infor.pay_type_text || ''}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nr\" v-if=\"infor.status != 0\">\r\n\t\t\t\t\t<view>支付时间：{{infor.paytime_text || ''}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"foot\" v-if=\"type == 2\">\r\n\t\t\t<view v-if=\"infor.status == 0\" @click=\"getCancel\">取消订单</view>\r\n\t\t\t<view class=\"active\" v-if=\"infor.status == 0\" @click=\"goPay(infor.order_sn,infor.type)\">立即支付</view>\r\n\t\t\t<view class=\"active\" v-if=\"infor.status == 2\" @click=\"getComplete\">确认收货</view>\r\n\t\t\t<view @click=\"getCall\">联系客服</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<uni-popup ref=\"showModal\" type=\"center\" :mask-click=\"false\">\r\n\t\t\t<view class=\"tc_instruction\">\r\n\t\t\t\t<view class=\"title\">使用须知</view>\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<rich-text :nodes=\"infor.xuzhi\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"getClose\">知道了</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\t\r\n\t\t\t\ttype: '',// 1柜订单详情  2商城订单详情\r\n\t\t\t\tid: '',// 订单ID\r\n\t\t\t\t\r\n\t\t\t\tinfor: '',// 详情\r\n\t\t\t\tkefu_tel: '',// 客服电话\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\tthis.type = e.type\r\n\t\t\tthis.id = e.id\r\n\t\t\tthis.getDetails()\r\n\t\t\tthis.getUser()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 个人资料\r\n\t\t\tgetUser(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.userInfo(params).then(res => {\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.kefu_tel = res.data.data.kefu_tel\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 详情\r\n\t\t\tgetDetails(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(that.type == 2){\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\torder_id: that.id,\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$api.OrderDetail(params).then(res => {\r\n\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\tthat.infor = res.data.data\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tid: that.id,\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$api.machineOrderDetail(params).then(res => {\r\n\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\tthat.infor = res.data.data\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消订单\r\n\t\t\tgetCancel(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确定取消订单?',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\torder_id: that.infor.id,\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$api.cancel(params).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: '订单取消成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthat.getDetails();\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认收货\r\n\t\t\tgetComplete(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认收货?',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\tid: that.infor.id,\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$api.complete(params).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthat.getDetails();\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 去支付\r\n\t\t\tgoPay(e,t){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(t == 'store'){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesPay/index?type=3' + '&order_sn=' + e\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesPay/index?type=' + that.type + '&order_sn=' + e\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 复制\r\n\t\t\tgetCopy(e){\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t    data: e, // e是需要设置的内容\r\n\t\t\t\t    success: function () {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:'复制成功',\r\n\t\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t    }\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 使用说明\r\n\t\t\topenShow(){\r\n\t\t\t\tthis.$refs.showModal.open()\r\n\t\t\t},\r\n\t\t\tgetClose(){\r\n\t\t\t\tthis.$refs.showModal.close()\r\n\t\t\t},\r\n\t\t\tgoMore(){\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '/pages/index/vending'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 联系客服\r\n\t\t\tgetCall() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: that.kefu_tel.toString()\r\n\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.top{\r\n\t\tposition: relative;\r\n\t}\r\n\t.top > image{\r\n\t\twidth: 100%;\r\n\t}\r\n\t.top .nr{\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding: 42rpx 52rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t}\r\n\t.top .nr image{\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t}\r\n\t.top .nr .text{\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\t.top .nr .text view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\t.top .nr .text text{\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin-top: 8rpx;\r\n\t}\r\n\t.order{\r\n\t\tpadding: 20rpx 24rpx;\r\n\t}\r\n\t.order.active{\r\n\t\tpadding-bottom: 110rpx;\r\n\t}\r\n\t.order .address{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 28rpx 22rpx 45rpx;\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat center;\r\n\t\tbackground-size: 100% auto;\r\n\t}\r\n\t.order .address .xx{\r\n\t\tpadding-left: 88rpx;\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat left center;\r\n\t\tbackground-size: 64rpx auto;\r\n\t}\r\n\t.order .address .xx .name{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-bottom: 6rpx;\r\n\t}\r\n\t.order .address .xx .name view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .address .xx .name view:last-child{\r\n\t\tmargin-left: 8rpx;\r\n\t}\r\n\t.order .address .xx .dz{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .product{\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .product .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 48rpx;\r\n\t}\r\n\t.order .product .nr:first-child{\r\n\t\tmargin-top: 0;\r\n\t}\r\n\t.order .product .nr .pic{\r\n\t\twidth: 184rpx;\r\n\t\theight: 184rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t.order .product .nr .pic image{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.order .product .nr .xx{\r\n\t\twidth: calc(100% - 204rpx);\r\n\t}\r\n\t.order .product .nr .xx .name{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t}\r\n\t.order .product .nr .xx .num{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t.order .product .nr .xx .price{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.order .product .nr .xx .more{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: red;\r\n\t\tbackground: ;\r\n\t}\r\n\t.order .product .instruction{\r\n\t\tborder-bottom: 1rpx solid #E4E4E4;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 108rpx;\r\n\t\tcolor: #666;\r\n\t\tpadding-right: 20rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAZCAYAAAABmx/yAAAAAXNSR0IArs4c6QAAAeRJREFUOE+N0r+LE0EUB/DvWwQLCRZa2dpoIUpmZkmvXfoI54nVYcTTg/PsLAKWEg9/cid31Smi2KaxCYjixX2JpA7Zf8BCYpvcPJllNuzd7eZ22nmf+T7ePGLmDQBPALS11k9R8hAz/wNQcfVEtKmUWi9jHXwJ4EFaLCLPtdYbRCSLHiB3GUXRHhEtZwrbSqnHi3AC3WHmdwBWMsmvtdZrRGTzkufQXfb7/S0RuZspfBvH8cNGo3FwFB+Cvu1tInLJyR0RvRmPx2tH8THo237hB5be7yilmkQ0T86FHrtpr6bJAHbiOG6myYXQt/2KiO5n8K5SasVNeyH0yc8APEqxiLzvdDp3ToQ+uU1E2Y3aLQU9/k1E1/y3/C0FmfkngFpmOVYXwlarFdTr9X0iMhm0bozZLITdbvdUpVIZALiSQU1jzHayGHl7OBqNTk8mk6gI5cLhcHhmOp3+AHA18+g9rfVWNuRQIjOfBbAP4FJaFATBUrVa/Vi45L1e71wQBN+JKEUiIreMMcfQvFVmPg/gG4DL/mUhottKqQ95M0jgYDC4YK116KIvsiJy0xjzpQglMIqiX+k/iciBiCyFYfh5EUogM/8B4FqdWWuXwzD8dBJKW71urb0xm826tVrtaxnkav4DqX3HuvoI+RYAAAAASUVORK5CYII=) no-repeat right center;\r\n\t\tbackground-size: 14rpx auto;\r\n\t}\r\n\t.order .product .ma{\r\n\t\tmargin-top: 36rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.order .product .ma image{\r\n\t\twidth: 260rpx;\r\n\t\theight: auto;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto 10rpx;\r\n\t}\r\n\t.order .product .ma view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .money{\r\n\t\tpadding: 30rpx 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.order .money .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.order .money .nr view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .money .nr text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .money .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\t.order .money .xx view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .money .xx text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.order .order_xx{\r\n\t\tpadding: 20rpx 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t.order .order_xx .nr{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.order .order_xx .nr view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.order .order_xx .nr .btn{\r\n\t\tfont-size: 22rpx;\r\n\t\tline-height: 52rpx;\r\n\t\tcolor: #161513;\r\n\t\tbackground: #F6F6F6;\r\n\t\tborder-radius: 26rpx;\r\n\t\tpadding: 0 28rpx;\r\n\t}\r\n\t\r\n\t.foot{\r\n\t\tbackground: #fff;\r\n\t\tpadding: 13rpx 24rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\t.foot view{\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground: #F6F6F6;\r\n\t\tmargin-left: 20rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t.foot view:first-child{\r\n\t\tmargin-left: 0;\r\n\t}\r\n\t.foot view.active{\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t}\r\n\t\r\n\t.foot button{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #FC451E;\r\n\t\tbackground: #F6F6F6;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tmargin: 0 0 0 20rpx;\r\n\t}\r\n\t.foot button::after{\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t\r\n\t\r\n\t.tc_instruction{\r\n\t\twidth: 630rpx;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 40rpx 40rpx 60rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t.tc_instruction .title{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\ttext-align: center;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 42rpx;\r\n\t}\r\n\t.tc_instruction .nr{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #666;\r\n\t\theight: 500rpx;\r\n\t\toverflow-y: auto;\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\t.tc_instruction .btn{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 72rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 46rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_details.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_details.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974040\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}