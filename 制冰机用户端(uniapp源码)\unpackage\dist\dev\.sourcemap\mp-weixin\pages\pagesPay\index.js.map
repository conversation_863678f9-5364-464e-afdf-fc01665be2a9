{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?6654", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?c2e4", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?927d", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?c159", "uni-app:///pages/pagesPay/index.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?6bf2", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesPay/index.vue?158e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "backgroud", "title", "time", "timer", "type", "order", "order_sn", "left_amount", "payment", "isWallet", "ps_tel", "onLoad", "console", "onShow", "uni", "url", "onHide", "clearInterval", "onUnload", "methods", "getPstel", "that", "res", "code", "msg", "icon", "duration", "catch", "bindOrder", "getOrder", "goWallet", "back", "countdown", "second", "minute", "setTimeout", "getPayment", "getPay", "mask", "content", "confirmText", "success", "paymentWechatHandler", "timeStamp", "nonceStr", "package", "signType", "paySign", "fail", "provider", "orderInfo", "getCall", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwDzuB;EACAC;IACA;MACAC;MACAC;MAGAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;;MAEAC;MAEAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACAC;MACA;MACA;MACA;MACA;IACA;IACA;MACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;MACA;QACAC;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAJ;IACAK;IACA;EACA;EACAC;IACAN;IACAK;IACA;EACA;EACAE;IACAC;MACA;MACA;MACAC;QACA,gBAKAC;UAJAC;UACAxB;UACAyB;UACAtB;QAEA;UACAmB;QACA;UACAP;YACAW;YACAxB;YACAyB;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;QACAtB;MACA;MACAe;QACA,iBAIAC;UAHAC;UACAxB;UACAyB;QAEA;UACAH;QACA;UACAP;YACAW;YACAxB;YACAyB;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAE;MACA;MACA;QACAvB;MACA;MACA;QACAe;UACA,iBAIAC;YAHAC;YACAxB;YACAyB;UAEA;YACAH;YACAA;YACAA;YACA;cACAA;YACA;cAEAA;YAKA;YACA;cACAA;YACA;YACAA;UACA;YACAP;cACAW;cACAxB;cACAyB;YACA;UACA;QACA,GACAC,sBAEA;MACA;QACAN;UACA,iBAIAC;YAHAC;YACAxB;YACAyB;UAEA;YACAH;YACAA;YACAA;YACA;cACAA;YACA;cAEAA;YAKA;YACA;cACAA;YACA;YACAA;UACA;YACAP;cACAW;cACAxB;cACAyB;YACA;UACA;QACA,GACAC,sBAEA;MACA;QACAN;UACA,iBAIAC;YAHAC;YACAxB;YACAyB;UAEA;YACAH;YACAA;YACAA;YACAA;UACA;YACAP;cACAW;cACAxB;cACAyB;YACA;UACA;QACA,GACAC,sBAEA;MACA;IACA;IACA;IACAG;MACAhB;QACAC;MACA;IACA;IACA;IACAgB;MACAd;MACA;MACAH;IACA;IACA;IACAkB;MACA;MACA;MACAX;QACAnB;QACA;QACA;QACA;UACA+B;QACA;QACAZ;QACA;UACAJ;UACAiB;UACAb;UACAP;YACAW;YACAxB;YACAyB;UACA;UACAS;YACA;cACArB;gBACAC;cACA;YACA;cACAD;gBACAC;cACA;YACA;cACAD;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAqB;MACA;MACA;QACA;UACAtB;YACAW;YACAxB;YACAyB;UACA;UACA;QACA;UACAL;QACA;MACA;QACA;UACAP;YACAW;YACAxB;YACAyB;UACA;UACA;QACA;UACAL;QACA;MACA;QACAA;MACA;IACA;IACA;IACAgB;MACA;MACA;QACAvB;UACAW;UACAxB;UACAyB;QACA;QACA;MACA;MACAZ;QACAb;QACAqC;MACA;MACArB;MACAI;MACA;QACAf;QACAE;MACA;MACA;QACA;QACAa;UACA,iBAIAC;YAHAC;YACAxB;YACAyB;UAEA;YACA;cACAV;cACAG;cACAI;cACAP;gBACAb;gBACAwB;gBACAa;gBACAZ;cACA;cACAZ;gBACAb;gBACAsC;gBACAC;gBACAC;kBACA;oBACApB;kBACA;oBACAP;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;cACAM;YACA;UACA;YACAP;YACAA;cACAb;cACAwB;cACAa;cACAZ;YACA;YACAS;cACArB;gBACAC;cACA;YACA;UACA;QACA,GACAY,sBAEA;MACA;QACAN;UACA,iBAIAC;YAHAC;YACAxB;YACAyB;UAEA;YACA;cACAV;cACAA;gBACAb;gBACAwB;gBACAa;gBACAZ;cACA;cACAS;gBACArB;kBACAC;gBACA;cACA;YACA;cACAM;YACA;UACA;YACAP;YACAA;cACAW;cACAxB;cACAqC;cACAZ;YACA;YACAS;cACA;gBACArB;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;YACA;UACA;QACA,GACAY,sBAEA;MACA;IACA;IACA;IACAe;MACA;MACA;QACA5B;UACA6B;UACAC;UACAC;UACAC;UACAC;UACAN;YACA3B;YACAA;cACAb;cACAwB;cACAa;cACAZ;YACA;YACA;cACAZ;gBACAb;gBACAsC;gBACAC;gBACAC;kBACA;oBACApB;kBACA;oBACAP;sBACAC;oBACA;kBACA;gBACA;cACA;YACA;cACAoB;gBACArB;kBACAC;gBACA;cACA;YACA;UACA;UACAiC;YACAlC;YACAA;cACAb;cACAwB;cACAa;cACAZ;YACA;YACAS;cACA;gBACArB;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;YACA;UACA;QACA;MACA;QACAD;UACAmC;UACAC;UACAT;YACA;cACA3B;cACAA;gBACAb;gBACAwB;gBACAa;gBACAZ;cACA;cACA;gBACAZ;kBACAb;kBACAsC;kBACAC;kBACAC;oBACA;sBACApB;oBACA;sBACAP;wBACAC;sBACA;oBACA;kBACA;gBACA;cACA;gBACAoB;kBACArB;oBACAC;kBACA;gBACA;cACA;YACA;UACA;UACAiC;YACAlC;YACAA;cACAb;cACAwB;cACAa;cACAZ;YACA;YACAS;cACA;gBACArB;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAoC;MACA;MACArC;QACAsC;MACA,yBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1mBA;AAAA;AAAA;AAAA;AAAshC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA1iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesPay/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesPay/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2f9d9b00&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesPay/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2f9d9b00&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"pay\">\r\n\t\t\t<view class=\"money\">\r\n\t\t\t\t<view class=\"time\">支付剩余时间 {{time?time:'00:00'}}</view>\r\n\t\t\t\t<view class=\"price\" v-if=\"type != 3\">￥{{order.pay_amount || '0.00'}}</view>\r\n\t\t\t\t<view class=\"integral\" v-else>\r\n\t\t\t\t\t<text>{{order.pay_amount || '0'}}</text>\r\n\t\t\t\t\t<view>积分</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"coupon\" v-if=\"type == 1\">优惠券抵扣{{order.coupon_amount || '0'}}元</view>\r\n\t\t\t\t<view class=\"coupons\" v-if=\"type == 1\">\r\n\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t<image src=\"/static/coupon_tb.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t<view>优惠券</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text>-￥{{order.coupon_amount || '0.00'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"voucher\" v-if=\"order.discount_amount > 0\">\r\n\t\t\t\t<view>兑换券</view>\r\n\t\t\t\t<text>-￥ {{order.discount_amount}}</text>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"payway\">\r\n\t\t\t\t<view class=\"title\">选择支付方式</view>\r\n\t\t\t\t<view class=\"nr\">\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<view class=\"xx\" :class=\"payment=='wechat'?'active':''\" @click=\"getPayment('wechat')\" v-if=\"type != 3 && order.pay_amount > 0\">\r\n\t\t\t\t\t\t<image src=\"/static/icon_fxwx.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>微信支付</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<view class=\"xx\" :class=\"payment=='wallet'?'active':''\" v-if=\"type != 3 && type != 5\" @click=\"getPayment('wallet')\">\r\n\t\t\t\t\t\t<image src=\"/static/icon_ye.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>余额支付<text>（余额￥{{left_amount}}）</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #ifdef MP-ALIPAY -->\r\n\t\t\t\t\t<view class=\"xx\" :class=\"payment=='alipay'?'active':''\" @click=\"getPayment('alipay')\" v-if=\"type != 3 && order.pay_amount > 0\">\r\n\t\t\t\t\t\t<image src=\"/static/icon_zfb.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>支付宝支付</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<view class=\"xx\" :class=\"payment=='score'?'active':''\" v-if=\"type == 3\" @click=\"getPayment('score')\">\r\n\t\t\t\t\t\t<image src=\"/static/icon_jf.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>积分支付<text>（剩余积分{{left_amount}}）</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn\" @click=\"goWallet\" v-if=\"isWallet\">余额充值</view>\r\n\t\t\t<view class=\"btn\" @click=\"getPay\">确认支付  {{type!=3?'￥' + order.pay_amount:order.pay_amount + '积分'}}</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbackgroud: '#fff',\r\n\t\t\t\ttitle: '收银台',\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\ttime:'',// 倒计时\r\n\t\t\t\ttimer: null, //定时器\r\n\t\t\t\ttype: '',// 1柜子订单 2商城订单 3积分商品 5余额充值\r\n\t\t\t\torder: '',// 订单信息\r\n\t\t\t\torder_sn: '',// 订单号\r\n\t\t\t\tleft_amount: '',// 剩余积分/余额\r\n\t\t\t\t\r\n\t\t\t\tpayment:'',// 支付方式 wechat（微信支付）,alipay(支付宝支付),wallet(余额),score(积分支付),card套餐卡,xinyi心意\r\n\t\t\t\t\r\n\t\t\t\tisWallet: false,\r\n\t\t\t\t\r\n\t\t\t\tps_tel: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\t// 扫码进入\r\n\t\t\tif(option.q){\r\n\t\t\t\tlet qrUrl = decodeURIComponent(option.q)\r\n\t\t\t\tconsole.log(qrUrl)\r\n\t\t\t\tlet type = this.$util.getQueryString(qrUrl, 'type')\r\n\t\t\t\tlet order_sn = this.$util.getQueryString(qrUrl, 'order_sn')\r\n\t\t\t\tthis.$cache.updateCache('type', type)\r\n\t\t\t\tthis.$cache.updateCache('order_sn', order_sn)\r\n\t\t\t}\r\n\t\t\tif(option.type){\r\n\t\t\t\tthis.type = option.type\r\n\t\t\t\tthis.order_sn = option.order_sn\r\n\t\t\t\tthis.getOrder()\r\n\t\t\t}\r\n\t\t\tthis.getPstel()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif(this.$cache.fetchCache('type')){\r\n\t\t\t\tif (!this.$cache.fetchCache('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.type = this.$cache.fetchCache('type')\r\n\t\t\t\tthis.order_sn = this.$cache.fetchCache('order_sn')\r\n\t\t\t\tthis.bindOrder()\r\n\t\t\t\tthis.$cache.updateCache('type', '')\r\n\t\t\t\tthis.$cache.updateCache('order_sn', '')\r\n\t\t\t}\r\n\t\t},\r\n\t\tonHide(){\r\n\t\t\tconsole.log('清除定时器1')\r\n\t\t\tclearInterval(this.timer);\r\n\t\t\tthis.timer = null;\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tconsole.log('清除定时器2')\r\n\t\t\tclearInterval(this.timer);\r\n\t\t\tthis.timer = null;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetPstel(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.getPstel(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.ps_tel = data\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 绑定售货机订单\r\n\t\t\tbindOrder(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\torder_sn: that.order_sn,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.bindOrder(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.getOrder()\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 订单数据\r\n\t\t\tgetOrder(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\torder_sn: that.order_sn,\r\n\t\t\t\t};\r\n\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\tthat.$api.OrdercontM(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tthat.time = data.order_close_time\r\n\t\t\t\t\t\t\tthat.order = data.order\r\n\t\t\t\t\t\t\tthat.left_amount = data.user_money\r\n\t\t\t\t\t\t\tif(data.user_money >= data.order.pay_amount){\r\n\t\t\t\t\t\t\t\tthat.payment = 'wallet'\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\tthat.payment = 'wechat'\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY \r\n\t\t\t\t\t\t\t\tthat.payment = 'alipay'\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(data.user_money < 5){\r\n\t\t\t\t\t\t\t\tthat.isWallet = true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.countdown()\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}else if(that.type == 2){\r\n\t\t\t\t\tthat.$api.Ordercont(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tthat.time = data.order_close_time\r\n\t\t\t\t\t\t\tthat.order = data.order\r\n\t\t\t\t\t\t\tthat.left_amount = data.left_amount\r\n\t\t\t\t\t\t\tif(data.user_money >= data.order.pay_amount){\r\n\t\t\t\t\t\t\t\tthat.payment = 'wallet'\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\tthat.payment = 'wechat'\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY \r\n\t\t\t\t\t\t\t\tthat.payment = 'alipay'\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(data.user_money < 5){\r\n\t\t\t\t\t\t\t\tthat.isWallet = true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.countdown()\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.$api.Ordercont(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tthat.time = data.order_close_time\r\n\t\t\t\t\t\t\tthat.order = data.order\r\n\t\t\t\t\t\t\tthat.left_amount = data.left_amount\r\n\t\t\t\t\t\t\tthat.countdown()\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 余额充值\r\n\t\t\tgoWallet(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesMy/recharge'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 返回\r\n\t\t\tback() {\r\n\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\tthis.timer = null;\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\t// 倒计时\r\n\t\t\tcountdown() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet time = that.time;\r\n\t\t\t\tthat.timer = setInterval(function(){\r\n\t\t\t\t\ttime = time - 1;\r\n\t\t\t\t\tvar minute = parseInt(time / 60);\r\n\t\t\t\t\tvar second = parseInt(time % 60);\r\n\t\t\t\t\tif(second < 10){\r\n\t\t\t\t\t\tsecond = '0' + second\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.time = minute + ':' + second\r\n\t\t\t\t\tif(time == 0) {\r\n\t\t\t\t\t\tclearInterval(that.timer);\r\n\t\t\t\t\t\tminute = second = \"00\";\r\n\t\t\t\t\t\tthat.timer = null;\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '支付超时',\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/sale_order'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}else if(that.type == 5){\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/balance'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/shop_order'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t}\r\n\t\t\t\t},1000);\r\n\t\t\t},\r\n\t\t\t// 选择支付方式\r\n\t\t\tgetPayment(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(e == 'wallet'){\r\n\t\t\t\t\tif(that.order.pay_amount*1 > that.left_amount*1){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '余额不足',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.payment = e\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(e == 'score'){\r\n\t\t\t\t\tif(that.order.pay_amount*1 > that.left_amount*1){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '剩余积分不足',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.payment = e\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.payment = e\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 支付\r\n\t\t\tgetPay(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(!that.payment){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '请选择支付方式',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '支付中',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t});\r\n\t\t\t\tclearInterval(that.timer);\r\n\t\t\t\tthat.timer = null;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\torder_sn: that.order_sn,\r\n\t\t\t\t\tpayment: that.payment,\r\n\t\t\t\t};\r\n\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t// 柜子订单\r\n\t\t\t\t\tthat.$api.createPayM(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tif(that.payment == 'wallet'){\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tclearInterval(that.timer);\r\n\t\t\t\t\t\t\t\tthat.timer = null;\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '如需配送请拨打',\r\n\t\t\t\t\t\t\t\t\tcontent: that.ps_tel,\r\n\t\t\t\t\t\t\t\t\tconfirmText: '配送',\r\n\t\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.getCall()\r\n\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tthat.paymentWechatHandler(data.pay_data)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.$api.createPay(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tif(that.payment == 'wallet'){\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tthat.paymentWechatHandler(data.pay_data)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tif(that.type == 5){\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/balance'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/shop_order'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 第三方支付\r\n\t\t\tpaymentWechatHandler(paymentParams){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(that.payment == 'wechat'){\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\ttimeStamp: paymentParams.timeStamp,\r\n\t\t\t\t\t\tnonceStr: paymentParams.nonceStr,\r\n\t\t\t\t\t\tpackage: paymentParams.package,\r\n\t\t\t\t\t\tsignType: paymentParams.signType,\r\n\t\t\t\t\t\tpaySign: paymentParams.paySign,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '如需配送请拨打',\r\n\t\t\t\t\t\t\t\t\tcontent: that.ps_tel,\r\n\t\t\t\t\t\t\t\t\tconfirmText: '配送',\r\n\t\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.getCall()\r\n\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t    },\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"支付失败\",\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else if(that.type == 5){\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/balance'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/shop_order'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\tprovider: 'alipay',\r\n\t\t\t\t\t    orderInfo: paymentParams.trade_no,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif(res && res.resultCode == '9000') {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '如需配送请拨打',\r\n\t\t\t\t\t\t\t\t\t\tcontent: that.ps_tel,\r\n\t\t\t\t\t\t\t\t\t\tconfirmText: '配送',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.getCall()\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\turl: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"支付失败\",\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tif(that.type == 1){\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else if(that.type == 5){\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/balance'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/pagesMy/shop_order'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tgetCall() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: that.ps_tel\r\n\t\t\t\t}).catch((res) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.pay{\r\n\t\tpadding: 20rpx 24rpx 0;\r\n\t}\r\n\t.pay .money{\r\n\t\tborder-radius: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 40rpx 24rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.pay .money .time{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 26rpx;\r\n\t}\r\n\t.pay .money .price{\r\n\t\tfont-size: 68rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.pay .money .integral{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: flex-end;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.pay .money .integral text{\r\n\t\tfont-size: 68rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.pay .money .integral view{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 74rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.pay .money .coupon{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #FC451E;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t.pay .money .coupons{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground: #F4F4F4;\r\n\t\tpadding: 0 34rpx;\r\n\t\tmargin-top: 72rpx;\r\n\t}\r\n\t.pay .money .coupons .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t}\r\n\t.pay .money .coupons .xx image{\r\n\t\twidth: 56rpx;\r\n\t\theight: auto;\r\n\t}\r\n\t.pay .money .coupons .xx view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 98rpx;\r\n\t\tcolor: #161513;\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\t.pay .money .coupons text{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 98rpx;\r\n\t\tcolor: #FC451E;\r\n\t\t/* padding-right: 24rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAZCAYAAAABmx/yAAAAAXNSR0IArs4c6QAAAVtJREFUOE+d1CFPw0AUB/D3r2i/QhPMggEHiiBJMMjJyam9qxsOXHHDQUV7pwhBADNIDAkJCBIMweEwJMh+gx65pWtua9feqLvk/frvvd47pGm66Xnei9b6JoqiU3J8IKX8AbBh6ouimLhiA68ADK2gmJnPuoJhCpRSt0Q0mBdrrU+EEOdteAb/gytY4gci6lvJYyHEZVPyAizxIxEdzYuLohBRFKllXIOuuBEaLKV8BnBgJQ2Z+Xq+XglL/AZg39rzQAhxb9atsA13wnLPH0S0ayX3nWCWZT3P877tznbCJEmCIAi+iKhn77UVGuT7/ieAreXuroRtaGVXp9Opn+f5OxHtWEmCmasTVEuM49gPw/AVwF71s4HxaDRaOLML0BXVPlVK+QTg0GUuq8R1b4IZXBfNPlVKmQEQVvcmzNx52xn4CyAs4QUzHzdNfG2QlVLbWmsze3euyLzkD1hcoRoBBLhzAAAAAElFTkSuQmCC) no-repeat right center;\r\n\t\tbackground-size: 14rpx auto; */\r\n\t}\r\n\t.pay .voucher{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 32rpx 0 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.pay .voucher{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tpadding: 0 22rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.pay .voucher view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.pay .voucher text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #FF2F00;\r\n\t}\r\n\t.pay .payway{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 184rpx;\r\n\t}\r\n\t.pay .payway .title{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t\tpadding: 0 22rpx;\r\n\t}\r\n\t.pay .payway .nr{\r\n\t\tpadding: 0 22rpx;\r\n\t}\r\n\t.pay .payway .nr .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #F6F6F6;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA2lJREFUWEfNV01oXFUUPucmeVCMEFzVlZXaSgloU2QmeedkU5TqSoJQ3VQQ7KatG6slKKK01LZUI90odKMLRaRCFv4uiovmnjc/i462ZGVtKqW2u1Asrcybeaec6UyYTKfNm3lp44PhDtxzvu+79557zzkIKT9VxXK5vCmO460AMAYANtrPvt8BoGLj4OBgJZ/PX0BETQONaYxE5CMA2KOqi0binGuRGTHUarWtSZI0RCHimKoOA8AXzHxoJfz7Coii6ClVnVXVU86542EY3loJ0OajKFoHANNJkrw8NDQ0lc/nF+7ld08BInIAAN5U1Slmnk9D3GlTLBafqdVqs4j4ORF92g2jqwAROWMLIaLpfog7fbz3x+1oiOj5zrm7BDTJTxLR16tB3sIQkTdUdRczb2/HXSague2PrdbKOxcgIp8AwD9ENNOaWxLQDLifiWjzaq68y3FcHBgY2D4xMXHJ5pYEiMh5VX2t34BLK3pubu5Z59xXRGTX9o4Au+eqmjDzwbRAWexExN6H/4joMNoLF0XRNUTckPaeZyE33/n5+eHFxcU/mflxLJVKm+M4/oGZn84K3Iu/iFwAgB3ovd8JAK8w86u9AGS1FZFTAPCdCTjinLsehuHRrKC9+Hvv30PER0zAL865E2EY/toLQFZb7/1LAPCWCbgaBMFYLpe7lhW0F/9yuby+Wq1W/hcC1vwIPgaAf5n5SC9bmNW2PQjX6hp+DwDfYrFY3FSv13960EmoS1L6S1VfaDzFInLFObfxYT3F3vtH7UUmoicaych7/4FzbjAMww+znm0afxE5rKo3LO6W0rH33krq18fHx8+lAenXplAobKvX6yeZ+bll9UCpVHoyjuPTzLyxX/A0ft77v4Mg4Fwud3mZgGZdsF9V1zPzu2nAerXx3n/mnLsUhuGJlm+3ovQ0AHxDRF/2SnA/exHZbVmXiF5st+talnvvf0PEs0T0zmqIsJUj4pZO8ruOoJ1MRN5W1X3WmExOTv7Rj5BmwM0652bat33FHWgZFAqFDUmSzALAjyMjI8dGR0dvpBFi9xwRp1V1RxAEU62A6+abtjl9X1X3IuJN64JV1ZrTShAENkK1WrUKdwwRt6mq/R9qNqcr5pdUAlrKRcSuaIOsSdQorU0MIpqYs0mSVJh5IW17fhu6dI9iqNFKkgAAAABJRU5ErkJggg==) no-repeat right center;\r\n\t\tbackground-size: 32rpx 32rpx;\r\n\t}\r\n\t.pay .payway .nr .xx:last-child{\r\n\t\tborder: none;\r\n\t}\r\n\t.pay .payway .nr .xx.active{\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAAAXNSR0IArs4c6QAAA+BJREFUWEfFmGuIVGUYx///c86cdXMbSXcDqciki2Ds7Qz0pYLUoiBDv5jFopa0QVHZRc/sFkQQ28zKgisVFnQxKoMiFxRSTM0PfUg7s65kkpEgQkHtRruVMXMuT5wZZ/Y2Z87ZmbHeb4f3ef7P733e63OIObTlT0hTw5XuXaSsJHALgGsBXHdJ4gLACwI5K8LD2T/Vo6ff5F9R5RnF0OjJ3UaPLwuwikQsio8IbAJfiqK9Yr3Gb8J8KoIkeuVWcZ1XATxAIhL0zIAiEBBD4mkvZvp5JggoULzTzHaRyjsE9LDRROkXICfibc6kGz4sZ18WxDDtJIC+arMQBFbIjrLVSqkDM21mgRg97tMUbzDKKKu1EZEtVlqfFmMaSGfSXqEABwFo1QaJ6Oe4Hu4d7o8dLtqXQNqevXhNTI+dArEwoliNZhxzvWzrcP/8n32hEohh5t4jualG9Tm6y7vfpvTNJZA2M7tco3KKgDJHpVrNHVu09pE0T+czYpjOXlLW1Kpajb+An1opbR2NbrmCC50xAPOqEQryaZoHdN2hwDonOPGTVJK+KL9rLTS2ZddQUfbWE2JRE7CrW8WSFsJxgfWDDs7/FhxBPG8tE9uct6BId71ApkL4mq4HPLgjBATcRSNpHyRwTz1AykH07nFx5LuKU+OHPuCDnCGwLAzkpsUFix9/KW9ZAwQE+J6JpD0OIF4J5PZlxPYuNW8y+IWLT76ePsJaIHxNEfwRCeSxlQq6V00eMQP7J2FqhbiUgAkf5AcAN1fKSKMO7HxERfuSyavJhzk0IqXdUVyYEdfEzHBnmTDtYyDuDFsj5WBGJwTN8QKcvzuqhPCn5isaSWc3IRvCQPz+cjC1QuTXCPg+O8zsepXKniggRZjXH1XRen0hE54APR9H2qKBIVzxHuKNT43FF8yP/0qgISpMvBF4+3EVS68mUkMuPj8eek4ESvtPyPG/J1r+/0tPOGSltbV5kPatuXZVpfVfPwME8BzxWkfSDYVngN8MM/cRyYejTk897ETkAyutb/S1SiCtL/xzg65pwwAW1CNIuAbHXdtuGx5oPD8NxP/oMJ37VMg+EIXz/PI1xwNWZ1KxA8UQs8sJ036OxKy6o55M4skzVr++c6pm2QKr08w9qZA7LkNZ4XgiWzJp/Y2ZAwsuOZP2CgKfEbiqTtkY84B1mVTsSDm9ioW10SuL4dp9IDdUvbXFv4a4m6r6ktXHgNfMlF1TadSJZK4DYB8Ed0deyAIXxCHHlZ6T2/WTYVmd068G43lpFtVerZD3A1gKchFEmvNByFGI+NXAOU9kP93YPmuAo2EAxf5/AU6Eibh+lv02AAAAAElFTkSuQmCC) no-repeat right center;\r\n\t\tbackground-size: 32rpx 32rpx;\r\n\t}\r\n\t.pay .payway .nr .xx image{\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t}\r\n\t.pay .payway .nr .xx view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 22rpx;\r\n\t}\r\n\t.pay .payway .nr .xx view text{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #FC451E;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\t.pay .btn{\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 44rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.pay .btn:last-child{\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974037\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}