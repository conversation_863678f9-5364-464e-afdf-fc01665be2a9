{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?b108", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?36e3", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?a56c", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?81a6", "uni-app:///pages/index/index.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?eaf6", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/index/index.vue?aea8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "backgroud", "show", "time", "banner", "nav", "notice", "recommend_goods", "order_id", "isTc", "regard", "coupon_id", "coupon", "onPageScroll", "watch", "onLoad", "onShow", "setTimeout", "onShareAppMessage", "console", "path", "methods", "goSearch", "uni", "url", "getReceive", "that", "res", "code", "msg", "icon", "title", "duration", "mask", "catch", "bindUser", "id", "getCoupon", "getData", "goIntegral", "goClassification", "goList", "goDetails", "goGoods", "getAddcar", "goods_list", "goCar", "getClose", "goCheck"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmFzuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MAAA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC;IACA;IACA;IACA;MACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;IACA;MACA;IACA;IACAC;MACA;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAF;UACAC;QACA;QACA;MACA;MACA;QACAhB;MACA;MACAkB;QACA,gBAIAC;UAHAC;UACA7B;UACA8B;QAEA;UACAH;UACAH;YACAO;YACAC;YACAC;YACAC;UACA;QACA;UACAV;YACAO;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAC;MACA;MACA;QACAZ;UACAC;QACA;MACA;QACA;UACAY;QACA;QACAV;UACA,iBAIAC;YAHAC;YACA7B;YACA8B;UAEA;YACAH;UACA;YACAH;cACAO;cACAC;cACAC;YACA;UACA;QACA,GACAE,sBAEA;MACA;IACA;IACA;IACAG;MACA;MACA;MACAX;QACA,iBAIAC;UAHAC;UACA7B;UACA8B;QAEA;UACA;YACA;YACAH;UACA;QACA;UACAH;YACAO;YACAC;YACAC;UACA;QACA;MACA,GACAE,sBAEA;IACA;IACA;IACAI;MACA;MACA;MACAZ;QACA,iBAIAC;UAHAC;UACA7B;UACA8B;QAEA;UACAH;UACAA;UACAA;UACAA;QACA;UACAH;YACAO;YACAC;YACAC;UACA;QACA;MACA,GACAE,sBAEA;IACA;IAEA;IACAK;MACAhB;QACAC;MACA;IACA;IACA;IACAgB;MACAjB;QACAC;MACA;IACA;IACA;IACAiB;MACAlB;QACAC;MACA;IACA;IAEA;IACAkB;MACAnB;QACAC;MACA;IACA;IAEA;IACAmB;MACApB;QACAC;MACA;IACA;IACA;IACAoB;MACA;MACA;QACArB;UACAC;QACA;MACA;QACA;UACAqB;YACA;YACA;UACA;QACA;QACAnB;UACA,iBAIAC;YAHAC;YACA7B;YACA8B;UAEA;YACAN;cACAO;cACAC;cACAC;YACA;UACA;YACAT;cACAO;cACAC;cACAC;YACA;UACA;QACA,GACAE,sBAEA;MACA;IACA;IAEA;IACAY;MACA;QACAvB;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IAEA;IACAuB;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAzB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxYA;AAAA;AAAA;AAAA;AAAshC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA1iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uniNavTouming: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-touming/uni-nav-touming\" */ \"@/components/uni-nav-touming/uni-nav-touming.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.nav, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.length > 5 && index < 5\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<uni-nav-touming :background-color=\"backgroud\" :fixed=\"true\" class=\"search_heard\" status-bar=\"true\">\r\n\t\t\t<view class=\"search\" @click=\"goSearch\">\r\n\t\t\t\t<image src=\"/static/iocn_search.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t</uni-nav-touming>\r\n\t\t<view class=\"banner_bg\">\r\n\t\t\t<swiper autoplay='true' circular=\"true\" interval='5000' duration='500' class='banner'>\r\n\t\t\t\t<swiper-item class=\"item_image\" v-for=\"(item,index) in banner\">\r\n\t\t\t\t\t<image :src='api + item.image' class='swiper_image' mode=\"aspectFill\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"nav\">\r\n\t\t\t<view class='nr' :class=\"item.length > 5 && index < 5?'mb':''\" v-for=\"(item,index) in nav\" @click=\"goList(item.path)\">\r\n\t\t\t\t<image :src=\"api + item.image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='nr' @click=\"goIntegral\">\r\n\t\t\t\t<image src=\"/static/icon_nav10.png\"></image>\r\n\t\t\t\t<view>积分商城</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class='nr' @click=\"goClassification\">\r\n\t\t\t\t<image src=\"/static/icon_nav11.png\"></image>\r\n\t\t\t\t<view>分类</view>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\r\n\t\t<view class=\"news\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<image src=\"/static/icon_new.png\" mode=\"widthFix\"></image>\r\n\t\t\t</view>\r\n\t\t\t<swiper class=\"swiper_container\" vertical=\"true\" autoplay=\"true\" circular=\"true\" interval=\"3000\">\r\n\t\t\t\t<swiper-item class=\"swiper_item\" v-for=\"(item,index) in notice\" @click.native=\"goDetails(item.id)\">\r\n\t\t\t\t\t<view>{{item.title}}</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"recom\" style=\"display: none;\">\r\n\t\t\t<view class=\"title\">为您推荐</view>\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"nr\" v-for=\"(item,index) in recommend_goods\" @click=\"goGoods(item.id)\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image :src=\"api + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<view class=\"numb\">\r\n\t\t\t\t\t\t\t\t<view>￥{{item.price}}</view>\r\n\t\t\t\t\t\t\t\t<view>￥{{item.market_price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"car\" @click.stop=\"getAddcar(item.id)\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/icon_carb.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- <view class=\"shopcar\" @click=\"goCar\">\r\n\t\t\t<image src=\"/static/icon_car.png\" mode=\"aspectFill\"></image>\r\n\t\t</view> -->\r\n\r\n\t\t<uni-popup ref=\"showModal\" type=\"center\" :mask-click=\"false\">\r\n\t\t\t<view class=\"tc_showModal\">\r\n\t\t\t\t<view class=\"coupon\">\r\n\t\t\t\t\t<image src=\"/static/cabinet_bj.png\" mode=\"widthFix\" @click=\"goCheck\"></image>\r\n\t\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t\t<view @click=\"getClose\"></view>\r\n\t\t\t\t\t\t<!-- <view @click=\"goCheck\">立即查看</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapMutations\r\n\t} from 'vuex'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\t\t\t\tbackgroud: 'rgba(255,255,255,0)',\r\n\t\t\t\tshow: false,\r\n\t\t\t\ttime: '.1s',\r\n\r\n\t\t\t\tbanner: [], // 轮播\r\n\r\n\t\t\t\tnav: [], // 分类\r\n\r\n\t\t\t\tnotice: [], // 公告\r\n\r\n\t\t\t\trecommend_goods: [], // 推荐商品\r\n\r\n\t\t\t\torder_id: '', // 分享的订单ID\r\n\t\t\t\tisTc: true, // 兑换券，优惠券弹层\r\n\t\t\t\tregard: '', // 兑换券信息\r\n\r\n\t\t\t\tcoupon_id: '', // 优惠券ID\r\n\t\t\t\tcoupon: '', // 优惠券\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\tif (e.scrollTop) {\r\n\t\t\t\tthis.backgroud = `rgba(255, 255, 255,${e.scrollTop / (e.scrollTop + 43.88)})`;\r\n\t\t\t} else if (e.scrollTop || e.scrollTop < 5) { // 这里<5 防止监听0 失败\r\n\t\t\t\tthis.backgroud = 'transparent'\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// hasLogin(news, olds) {\r\n\t\t\t// \tif (this.order_id) {\r\n\t\t\t// \t\tthis.getRegard()\r\n\t\t\t// \t}\r\n\t\t\t// \tif (this.coupon_id) {\r\n\t\t\t// \t\tthis.getCoupon()\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.api = this.$http.apiPic;\r\n\t\t\t// 点击分享订单进入\r\n\t\t\tif (option.order_id) {\r\n\t\t\t\tthis.$cache.updateCache('order_id', option.order_id)\r\n\t\t\t}\r\n\t\t\t// 点击邀请进入\r\n\t\t\tif (option.id) {\r\n\t\t\t\tthis.$cache.updateCache('id', option.id)\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif(this.$cache.fetchCache('order_id')){\r\n\t\t\t\tthis.getReceive()\r\n\t\t\t}\r\n\t\t\tif(this.$cache.fetchCache('id')){\r\n\t\t\t\tthis.bindUser()\r\n\t\t\t}\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthis.getCoupon()\r\n\t\t\t},300)\r\n\t\t\tthis.getData()\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tif (res.from === 'button') {\r\n\t\t\t\tconsole.log(res.target)\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/pages/index/index'\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 搜索\r\n\t\t\tgoSearch() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/goods_list'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 领取订单\r\n\t\t\tgetReceive(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (!that.$cache.fetchCache('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\torder_id: that.$cache.fetchCache('order_id')\r\n\t\t\t\t}\r\n\t\t\t\tthat.$api.receive(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.$cache.updateCache('order_id', '')\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: '领取成功',\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 绑定上下级\r\n\t\t\tbindUser() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (!that.$cache.fetchCache('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tid: that.$cache.fetchCache('id')\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$api.bindUser(params).then(res => {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\tthat.$cache.updateCache('id', '')\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 优惠券弹层\r\n\t\t\tgetCoupon() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.getCoupon(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tif(data.length > 0){\r\n\t\t\t\t\t\t\t// uni.hideTabBar()\r\n\t\t\t\t\t\t\tthat.$refs.showModal.open()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 轮播\r\n\t\t\tgetData() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.shopindex(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.banner = data.banner\r\n\t\t\t\t\t\tthat.nav = data.diamond\r\n\t\t\t\t\t\tthat.notice = data.notice\r\n\t\t\t\t\t\tthat.recommend_goods = data.recommend_goods\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 积分商城\r\n\t\t\tgoIntegral() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/integral_list'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 商品分类\r\n\t\t\tgoClassification() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/goods_classification'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 商品列表\r\n\t\t\tgoList(e) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 公告详情\r\n\t\t\tgoDetails(e) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/details?id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 商品详情\r\n\t\t\tgoGoods(e) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/goods_details?id=' + e\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 加入购物车\r\n\t\t\tgetAddcar(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (!that.$cache.fetchCache('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tgoods_list: [{\r\n\t\t\t\t\t\t\t\"goods_id\": e,\r\n\t\t\t\t\t\t\t\"goods_num\": 1,\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$api.Addcar(params).then(res => {\r\n\t\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\t\t\tmsg\r\n\t\t\t\t\t\t\t} = res.data\r\n\t\t\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\ttitle: '加入购物车成功',\r\n\t\t\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\ttitle: '加入购物车失败',\r\n\t\t\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 购物车\r\n\t\t\tgoCar() {\r\n\t\t\t\tif (!this.$cache.fetchCache('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesCar/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹层\r\n\t\t\tgetClose() {\r\n\t\t\t\tthis.$refs.showModal.close()\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \tuni.showTabBar()\r\n\t\t\t\t// },300)\r\n\t\t\t},\r\n\t\t\t// 查看\r\n\t\t\tgoCheck() {\r\n\t\t\t\tthis.$refs.showModal.close()\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pagesHome/coupons'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.search_heard,\r\n\t.uni-navbar {\r\n\t\twidth: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t}\r\n\r\n\t.search {\r\n\t\twidth: 65rpx;\r\n\t\theight: 65rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 200rpx;\r\n\t\tz-index: 99;\r\n\t}\r\n\r\n\t/*  #ifdef  MP-ALIPAY  */\r\n\t.search {\r\n\t\tright: 280rpx;\r\n\t}\r\n\r\n\t/*  #endif  */\r\n\t.search image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.banner_bg .banner {\r\n\t\twidth: 100%;\r\n\t\theight: 530rpx;\r\n\t}\r\n\r\n\t.banner_bg .banner .swiper_image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.news {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 0 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.news .title {\r\n\t\twidth: 46rpx;\r\n\t\tpadding-right: 20rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.news .title image {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.news .title::after {\r\n\t\tcontent: '';\r\n\t\twidth: 5rpx;\r\n\t\theight: 38rpx;\r\n\t\tbackground: #ccc;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t}\r\n\r\n\t.news .swiper_container {\r\n\t\twidth: calc(100% - 86rpx);\r\n\t\theight: 88rpx;\r\n\t\tpadding-right: 24rpx;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAZCAYAAAABmx/yAAAAAXNSR0IArs4c6QAAAeBJREFUOE+N079v00AUB/Dv10JiQIEBJlYWGBAodyexw5Y9DG3FVDWoQKVSNoZIjChU/FSLShd+lIE1C0skBKKJL0GZo/gfYECZQ/2QrXN0LbYTr+99/H2+e2av19sKguAxgJbW+gkWfGitnQA4m/ST3FZKbS5iE/gCwP2sWUSeaa23SErZC5gUwzB8T3LZa2wppR6V4RQmj7X2LYBVL/mV1nqDZJyXPINJsd/v74jImtf4JoqiB/V6/egkPgbd2Lskk+S0RvL1eDzeOIn/g27s5+7AsvqeUqpBcpacCx1OTvtelgxgL4qiRpZcCN3YL0mue/idUmo1Oe1S6JKfAniYYRH50G6378yFLrlFcrZRIrK/EHT4F8nr7lr+LASttT8B3PDucr0UNpvNoFarHZI03kZtGmO2C2Gn0zlVqVQGAK56qGGM2U0XI28PR6PR6clkEhahXDgcDs9Mp9MfAK55L72rtd7xQ44lWmvPATgEcNlrWtJafypc8m63ez4Igu8kMyQismSMOSj8ray1FwB8A3DFNQnJFaXUxzyUfuNgMLgYx3GCLrmmWERuG2O+FKEUhmHY8+7pSESWjTGfy1AKrbW/ASSj/g2CYKVarc5F2ag34zi+BaCjtf46Lymr/wNuz7667af5rwAAAABJRU5ErkJggg==) no-repeat right center;\r\n\t\tbackground-size: 14rpx auto;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.news .swiper_container .swiper_item view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tcolor: #161513;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.nav {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tbackground: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 22rpx 0 25rpx;\r\n\t}\r\n\r\n\t.nav .nr {\r\n\t\twidth: 20%;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 45rpx;\r\n\t}\r\n\r\n\t.nav .nr:nth-child(-n + 5) {\r\n\t\tmargin-top: 0;\r\n\t}\r\n\r\n\t.nav .nr image {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto 15rpx;\r\n\t}\r\n\r\n\t.nav .nr view {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.recom .title {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 35rpx;\r\n\t}\r\n\r\n\t.recom .list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 24rpx 32rpx;\r\n\t}\r\n\r\n\t.recom .list .nr {\r\n\t\twidth: 32%;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 2%;\r\n\t\tmargin-top: 24rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.recom .list .nr:nth-child(3n) {\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.recom .list .nr:nth-child(-n + 3) {\r\n\t\tmargin-top: 0;\r\n\t}\r\n\r\n\t.recom .list .nr .pic {\r\n\t\twidth: 100%;\r\n\t\theight: 333rpx;\r\n\t}\r\n\r\n\t.recom .list .nr .pic image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.recom .list .nr .xx {\r\n\t\tpadding: 10rpx 24rpx 15rpx;\r\n\t}\r\n\r\n\t.recom .list .nr .xx .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.recom .list .nr .xx .price {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.recom .list .nr .price .numb view {\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.recom .list .nr .price .numb view:last-child {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: normal;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.recom .list .nr .price .car {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t}\r\n\r\n\t.recom .list .nr .price .car image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.shopcar {\r\n\t\tposition: fixed;\r\n\t\twidth: 68rpx;\r\n\t\theight: 68rpx;\r\n\t\tright: 24rpx;\r\n\t\tbottom: 10%;\r\n\t}\r\n\r\n\t.shopcar image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\r\n\t.tc_showModal {\r\n\t\twidth: 522rpx;\r\n\t}\r\n\r\n\t.tc_showModal .coupon {\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.tc_showModal .coupon image {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.tc_showModal .coupon .btn {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 68rpx;\r\n\t}\r\n\r\n\t.tc_showModal .coupon .btn view {\r\n\t\twidth: 76rpx;\r\n\t\theight: 76rpx;\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat center;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974032\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}