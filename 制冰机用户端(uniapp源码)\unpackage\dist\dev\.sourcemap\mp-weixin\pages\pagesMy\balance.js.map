{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?de19", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?5585", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?b44f", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?c28f", "uni-app:///pages/pagesMy/balance.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?ea3d", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesMy/balance.vue?72f8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "list", "limit", "page", "loading", "onLoad", "methods", "getList", "setTimeout", "uni", "that", "icon", "title", "duration", "catch", "onReachBottom", "onPullDownRefresh", "go<PERSON>ech<PERSON>ge", "url", "go<PERSON><PERSON><PERSON>wal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAutB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwC3uB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAJ;QACAD;MACA;MACA;QACAM;UACAC;QACA;QACA;UACAC;UACA;YACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACAD;YACAE;YACAC;YACAC;UACA;QACA;MACA,GACAC;QACAN;UACAC;QACA;MACA;MACAD;QACAC;MACA;IACA;IACA;IACAM;MACA;MACA;MACA;IACA;IACA;IACAC;MACAP;QACAG;MACA;MACA;MACA;MACA;IACA;IACAK;MACAR;QACAS;MACA;IACA;IACAC;MACAV;QACAS;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAAwhC,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACA5iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesMy/balance.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesMy/balance.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./balance.vue?vue&type=template&id=04ea3318&\"\nvar renderjs\nimport script from \"./balance.vue?vue&type=script&lang=js&\"\nexport * from \"./balance.vue?vue&type=script&lang=js&\"\nimport style0 from \"./balance.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesMy/balance.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./balance.vue?vue&type=template&id=04ea3318&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./balance.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./balance.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<image src=\"/static/balance_top.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"nr\">\r\n\t\t\t\t<view class=\"title\">余额(元)</view>\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<text>{{money || '0.00'}}</text>\r\n\t\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t\t<!-- <view @click=\"goWithdrawal\">提现</view> -->\r\n\t\t\t\t\t\t<view @click=\"goRecharge\">立即充值</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<view class=\"data\" v-if=\"list.length > 0\">\r\n\t\t\t\t<view class=\"title\">余额明细</view>\r\n\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t<view class=\"nr\" v-for=\"(item,index) in list\">\r\n\t\t\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t\t\t<view class=\"name\">{{item.memo}}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">{{item.createtime}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pic\" :class=\"item.money < 0?'':'active'\">{{item.money < 0?item.money:'+'+item.money}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<uni-load-more :status=\"loading\" v-if=\"list.length > 0\"></uni-load-more>\r\n\t\t\t\r\n\t\t\t<view class=\"zw\" v-else>\r\n\t\t\t\t<image src=\"/static/icon_wsj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view>暂无余额明细</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\t\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmoney: '',// 余额\r\n\t\t\t\tlist: [],// 明细\r\n\t\t\t\t\r\n\t\t\t\tlimit: 10,// 每页条数\r\n\t\t\t\tpage: 1, //分页\r\n\t\t\t\tloading: \"noMore\", // noMore-无更多数据 loading-加载中 more-上拉加载更多\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 数据\r\n\t\t\tgetList(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t};\r\n\t\t\t\tthis.$api.userMoney(params).then(res => {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t\tif (res.data.code == 1) {\r\n\t\t\t\t\t\tthat.money = res.data.data.money\r\n\t\t\t\t\t\tif (that.page == 1) {\r\n\t\t\t\t\t\t\tthat.list = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor (var i = 0; i < res.data.data.list.length; i++) {\r\n\t\t\t\t\t\t\tthat.list.push(res.data.data.list[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.data.data.list.length <= that.limit) {\r\n\t\t\t\t\t\t\tthat.loading = 'noMore';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: res.data.msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.stopPullDownRefresh(); //得到数据后停止下拉刷新\r\n\t\t\t\t}, 300);\r\n\t\t\t},\r\n\t\t\t// 上拉加载\r\n\t\t\tonReachBottom() {\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t// 下拉刷新\r\n\t\t\tonPullDownRefresh() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tthis.loading = 'loading';\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\tgoRecharge(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: 'recharge'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoWithdrawal(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: 'withdrawal'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.top{\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #F2F2F2;\r\n\t}\r\n\t.top image{\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tdisplay: block;\r\n\t}\r\n\t.top .nr{\r\n\t\twidth: 100%;\r\n\t\tpadding: 56rpx 54rpx 0 70rpx;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.top .nr .title{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.top .nr .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.top .nr .xx text{\r\n\t\tfont-size: 46rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.top .nr .xx .btn{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.top .nr .xx .btn view{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #3478FB;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 32rpx;\r\n\t\tpadding: 0 32rpx;\r\n\t}\r\n\t/* .top .nr .xx .btn view:last-child{\r\n\t\tcolor: #70C5BE;\r\n\t\tbackground: #fff;\r\n\t\tmargin-left: 20rpx;\r\n\t} */\r\n\t.detail{\r\n\t\tpadding: 280rpx 24rpx 0;\r\n\t}\r\n\t.detail .data{\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t.detail .data .title{\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #454555;\r\n\t\tfont-weight: bold;\r\n\t\tborder-bottom: 1px solid #E4E4E4;\r\n\t}\r\n\t.detail .data .list{\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\t.detail .data .list .nr{\r\n\t\tpadding: 20rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #E4E4E4;\r\n\t}\r\n\t.detail .data .list .nr:last-child{\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t.detail .data .list .nr .xx .name{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.detail .data .list .nr .xx .time{\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.detail .data .list .nr .pic{\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.detail .data .list .nr .pic.active{\r\n\t\tcolor: #FC451E;\r\n\t}\r\n\t\r\n\t.zw{\r\n\t\tmargin-top: 20%;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./balance.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./balance.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757291974088\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}