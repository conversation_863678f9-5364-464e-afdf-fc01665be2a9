# 外卖订单创建功能实现

## 任务背景
用户在vending页面选择外卖模式后，需要能够创建外卖订单，包含配送地址、配送费计算、配送时间等功能。

## 数据库支持
订单表 `fa_vending_machine_order` 已包含外卖相关字段：
- `order_mode`: 订单模式(1=自取,2=外卖)
- `user_address_id`: 用户地址ID
- `delivery_consignee`: 收货人姓名
- `delivery_mobile`: 收货人手机
- `delivery_address`: 收货详细地址
- `dispatch_fee`: 配送费
- `rider_fee`: 骑手佣金
- `dispatch_status`: 配送状态
- `dispatch_type`: 配送类型(1=立即配送,2=预约配送)
- `expected_delivery_starttime`: 期望送达开始时间
- `expected_delivery_endtime`: 期望送达结束时间

## 实施方案

### 1. 扩展后端订单创建接口
**文件：** `application/api/controller/machine/Order.php`
**修改内容：**
- 扩展 `create()` 方法参数，支持外卖模式
- 添加参数：`order_mode`, `user_address_id`, `delivery_time`
- 添加外卖模式参数验证

### 2. 扩展订单服务类
**文件：** `application/common/service/machine/OrderService.php`
**修改内容：**
- 扩展 `create()` 方法支持外卖参数
- 添加 `calculateDeliveryFee()` 配送费计算方法
- 添加 `calculateDistance()` 距离计算方法
- 在订单数据中添加外卖相关字段处理

### 3. 前端API接口
**文件：** `制冰机用户端(uniapp源码)/http/api.js`
**修改内容：**
- 添加 `createDeliveryOrder()` 方法
- 调用相同的后端接口，通过参数区分模式

### 4. 前端订单提交
**文件：** `制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue`
**修改内容：**
- 实现 `submitOrder()` 方法的真正逻辑
- 添加参数验证和错误处理
- 构建外卖订单提交参数

## 核心功能

### 配送费计算逻辑
1. 获取用户收货地址的经纬度
2. 获取机器位置的经纬度
3. 计算两点间距离（使用球面距离公式）
4. 根据配送费模板计算配送费用
5. 计算骑手佣金（配送费的80%）

### 订单创建流程
1. **参数验证**：检查外卖模式必需参数
2. **配送费计算**：调用配送费计算方法
3. **地址信息处理**：提取收货人信息
4. **订单数据构建**：包含所有外卖相关字段
5. **配送时间处理**：支持立即配送和预约配送
6. **订单保存**：创建订单和订单商品记录

### 前端提交逻辑
1. **信息验证**：检查收货地址和订单数据
2. **参数构建**：组装外卖订单参数
3. **接口调用**：提交订单创建请求
4. **结果处理**：成功跳转支付，失败显示错误

## 技术特点
- **兼容性**：扩展现有接口，保持自取模式正常运行
- **完整性**：包含配送费计算、地址处理、时间管理
- **容错性**：配送费计算失败时使用默认值
- **用户体验**：完整的加载状态和错误提示

## 使用效果
- ✅ 支持外卖订单创建
- ✅ 自动计算配送费用
- ✅ 保存完整的配送信息
- ✅ 支持立即配送和预约配送
- ✅ 完整的错误处理和用户提示

## 支付流程修正

### 问题发现
用户指出自取订单的支付页面走的是 `/api/machine/pay/index` 接口，而不是 `/api/shop/pay`。

### 修正方案
外卖订单作为售货机订单的一种模式，应该与自取订单使用相同的支付流程：

**支付页面跳转参数修正：**
- 原来：`type=2` （商城订单支付）
- 修正：`type=1` （售货机订单支付）

**支付接口对应关系：**
- `type=1`：调用 `/api/machine/pay/create` （售货机支付）
- `type=2`：调用 `/api/shop/pay/create` （商城支付）

**修正效果：**
- ✅ 外卖订单使用正确的售货机支付接口
- ✅ 保持自取和外卖订单支付流程的一致性
- ✅ 支付页面能正确获取外卖订单信息

## goods_num参数统一修正

### 问题发现
用户指出外卖模式的goods_num处理与自取模式不一致：
- **自取模式**：固定规格goods_num=1，自定义规格goods_num=用户输入重量
- **外卖模式**：之前错误地都设置为重量值

### 修正方案

**1. vending页面逻辑修正**
```javascript
// 修正前：外卖模式都设置actualQuantity = 1
let actualQuantity = 1

// 修正后：与自取模式保持一致
let actualQuantity = that.goods_num // 使用与自取模式相同的goods_num

// 重量计算逻辑
if (selectedGoods.spec_num > 0) {
    // 固定规格：goods_num=1，使用商品默认重量
    actualWeight = selectedGoods.weight || 10
} else {
    // 自定义规格：goods_num=重量值，重量也是goods_num
    actualWeight = parseFloat(that.goods_num) || 10
}
```

**2. order-confirm页面修正**
```javascript
// 修正前：使用重量作为goods_num
goods_num: that.goodsInfo.weight

// 修正后：使用正确的goods_num
goods_num: that.goodsInfo.goods_num

// 配送费计算使用正确的重量字段
total_weight: that.goodsInfo.goods_weight
```

**3. 后端配送费计算修正**
```php
// 添加重量计算逻辑
$actualWeight = $goods_num;
if ($goods_num == 1) {
    // 固定规格商品，从商品信息中获取重量
    $goods = Goods::get($goods_id);
    if ($goods && $goods->weight > 0) {
        $actualWeight = $goods->weight;
    }
}
```

### 修正效果
- ✅ 外卖模式与自取模式使用统一的goods_num逻辑
- ✅ 固定规格商品：goods_num=1，重量从商品信息获取
- ✅ 自定义规格商品：goods_num=用户输入重量，重量=goods_num
- ✅ 配送费计算使用正确的重量参数
- ✅ 不影响现有的自取模式逻辑

## 配送费计算接口修正

### 问题发现
用户指出了两个重要问题：
1. **重量参数丢失**：order-confirm页面调用配送费计算接口时重量参数不正确
2. **重复开发**：系统已有完整的配送费计算接口，不需要重新开发

### 原有系统分析
系统已经有完整的配送费计算逻辑：
- **接口**：`/api/machine/order/calculateDeliveryFee`
- **功能**：距离计算、重量检查、配送范围验证、费用计算
- **前端调用**：order-confirm页面已在使用此接口

### 我的错误
1. **重复开发**：在OrderService中添加了多余的calculateDeliveryFee方法
2. **参数不匹配**：goodsInfo字段名不一致导致重量参数丢失
3. **架构破坏**：试图修改已有的成熟接口

### 正确的解决方案

**1. 移除重复代码**
- 删除OrderService中的calculateDeliveryFee方法
- 删除calculateDistance方法
- 保持原有接口不变

**2. 修正参数传递**
```javascript
// order-confirm页面保存完整的商品信息
this.goodsInfo = {
    goods_num: parseInt(goodsData.goods_num) || 1,
    goods_weight: parseFloat(goodsData.goods_weight) || 10
}

// 配送费计算使用正确的重量参数
total_weight: that.goodsInfo.goods_weight
```

**3. 配送费传递给订单创建**
```javascript
// 前端传递计算好的配送费
dispatch_fee: that.deliveryFee || 0

// 后端接收并使用前端计算的配送费
$dispatch_fee = $params['dispatch_fee'] ?? 0;
```

### 修正效果
- ✅ 使用现有的成熟配送费计算接口
- ✅ 重量参数正确传递给配送费计算
- ✅ 前端计算的配送费正确传递给订单创建
- ✅ 不破坏现有系统架构
- ✅ 避免重复开发和代码冗余

## 注意事项
1. 配送费计算依赖地址经纬度信息
2. 需要配置配送费模板才能正常计算费用
3. 骑手佣金比例可根据实际情况调整
4. 建议添加订单状态管理和配送跟踪功能
5. **重要**：外卖订单必须使用 `type=1` 跳转支付页面，确保使用售货机支付接口
6. **重要**：外卖模式的goods_num必须与自取模式保持一致，确保订单数据的统一性
7. **重要**：应该使用现有的配送费计算接口，不要重复开发
8. **重要**：配送费应该由前端计算后传递给订单创建接口
